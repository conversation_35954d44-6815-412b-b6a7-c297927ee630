#!/usr/bin/env python3
"""
安全文件同步系统测试脚本
"""

import os
import sys
import time
import tempfile
import threading
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from secure_sync import SyncServer, SyncClient, SecureProtocol, PacketType

def test_protocol():
    """测试协议功能"""
    print("测试协议功能...")

    try:
        protocol = SecureProtocol()

        # 测试数据包创建和解析
        test_data = b"Hello, Secure Sync!"
        packet = protocol.create_packet(PacketType.HEARTBEAT, test_data)

        result = protocol.parse_packet(packet)
        if result:
            packet_type, payload = result
            if packet_type == PacketType.HEARTBEAT and payload == test_data:
                print("✓ 协议测试通过")
                return True
            else:
                print(f"✗ 协议测试失败: 数据不匹配 {packet_type} vs {PacketType.HEARTBEAT}")
        else:
            print("✗ 协议测试失败: 无法解析数据包")
    except Exception as e:
        print(f"✗ 协议测试异常: {e}")

    return False

def test_file_sync():
    """测试文件同步功能"""
    print("测试文件同步功能...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        server_dir = os.path.join(temp_dir, "server")
        client_dir = os.path.join(temp_dir, "client")
        
        os.makedirs(server_dir)
        os.makedirs(client_dir)
        
        # 在服务端目录创建测试文件
        test_file = os.path.join(server_dir, "test.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文件\n用于验证同步功能")
        
        # 启动服务端
        server = SyncServer(server_dir, "127.0.0.1", 23848)  # 使用不同端口避免冲突
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        
        # 等待服务端启动
        time.sleep(2)
        
        try:
            # 启动客户端
            client = SyncClient("test_client", client_dir)
            
            if client.connect("127.0.0.1", 23848):
                print("✓ 客户端连接成功")
                
                # 等待同步完成
                time.sleep(5)
                
                # 检查文件是否同步
                client_test_file = os.path.join(client_dir, "test.txt")
                if os.path.exists(client_test_file):
                    with open(client_test_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if "这是一个测试文件" in content:
                        print("✓ 文件同步测试通过")
                        return True
                    else:
                        print("✗ 文件内容不匹配")
                else:
                    print("✗ 文件未同步")
                
                client.disconnect()
            else:
                print("✗ 客户端连接失败")
        
        finally:
            server.stop()
    
    return False

def test_security():
    """测试安全功能"""
    print("测试安全功能...")
    
    from secure_sync.security import SecurityManager
    
    security = SecurityManager()
    
    # 测试用户认证
    session_info = security.create_secure_session("sync_user", "SecurePassword2025!", "127.0.0.1")
    if session_info:
        session_token, session_key = session_info
        print("✓ 用户认证测试通过")
        
        # 测试数据加密
        test_data = b"Secret message for encryption test"
        encrypted = security.secure_packet(test_data, session_token)
        decrypted = security.unsecure_packet(encrypted, session_token)
        
        if decrypted == test_data:
            print("✓ 数据加密测试通过")
            return True
        else:
            print("✗ 数据加密测试失败")
    else:
        print("✗ 用户认证测试失败")
    
    return False

def run_performance_test():
    """运行性能测试"""
    print("运行性能测试...")

    try:
        protocol = SecureProtocol()

        # 测试大数据包处理
        large_data = b"X" * 10240  # 10KB数据

        start_time = time.time()
        for i in range(100):
            packet = protocol.create_packet(PacketType.FILE_DATA, large_data)
            result = protocol.parse_packet(packet)
            if not result:
                print(f"✗ 性能测试失败: 第{i+1}次解析失败")
                return False

        end_time = time.time()
        duration = end_time - start_time

        print(f"✓ 性能测试通过: 处理100个10KB数据包用时 {duration:.2f} 秒")
        print(f"  平均处理速度: {(100 * 10240 / 1024 / duration):.2f} KB/s")

        return True
    except Exception as e:
        print(f"✗ 性能测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("安全文件同步系统测试")
    print("=" * 60)
    
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)  # 减少测试时的日志输出
    
    tests = [
        ("协议功能", test_protocol),
        ("安全功能", test_security),
        ("性能测试", run_performance_test),
        ("文件同步", test_file_sync),  # 最后测试，因为需要更多时间
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n正在运行: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试异常: {test_name} - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == '__main__':
    sys.exit(main())
