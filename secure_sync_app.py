#!/usr/bin/env python3
"""
安全文件同步应用程序主入口
支持GUI模式、服务端模式和客户端模式
"""

import sys
import argparse
import logging
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from secure_sync import SyncG<PERSON>, SyncServer, SyncClient

def setup_logging(verbose: bool = False):
    """设置日志配置"""
    log_level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('secure_sync.log', encoding='utf-8')
        ]
    )

def run_gui():
    """运行GUI模式"""
    print("启动图形界面模式...")
    app = SyncGUI()
    app.run()

def run_server(args):
    """运行服务端模式"""
    print(f"启动服务端模式...")
    print(f"同步目录: {args.directory}")
    print(f"监听地址: {args.host}:{args.port}")
    
    if not os.path.exists(args.directory):
        print(f"错误: 同步目录不存在: {args.directory}")
        return 1
    
    server = SyncServer(args.directory, args.host, args.port)
    
    try:
        server.start()
        return 0
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止服务端...")
        server.stop()
        return 0
    except Exception as e:
        print(f"服务端运行异常: {e}")
        return 1

def run_client(args):
    """运行客户端模式"""
    print(f"启动客户端模式...")
    print(f"客户端ID: {args.client_id}")
    print(f"同步目录: {args.directory}")
    print(f"服务器地址: {args.server}:{args.port}")
    
    if not os.path.exists(args.directory):
        os.makedirs(args.directory, exist_ok=True)
        print(f"创建同步目录: {args.directory}")
    
    client = SyncClient(args.client_id, args.directory)
    
    try:
        if client.connect(args.server, args.port):
            print("客户端连接成功，按 Ctrl+C 停止...")
            while client.running:
                import time
                time.sleep(1)
            return 0
        else:
            print("无法连接到服务器")
            return 1
    except KeyboardInterrupt:
        print("\n收到中断信号，正在断开连接...")
        client.disconnect()
        return 0
    except Exception as e:
        print(f"客户端运行异常: {e}")
        return 1

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='安全文件同步工具 - 使用私有协议的隐蔽文件同步软件',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 启动图形界面
  python secure_sync_app.py gui
  
  # 启动服务端
  python secure_sync_app.py server -d /path/to/sync --host 0.0.0.0 --port 23847
  
  # 启动客户端
  python secure_sync_app.py client -d /path/to/sync -s ************* --port 23847 --client-id client_001

安全特性:
  - 使用非常用端口 (默认: 23847)
  - 自定义二进制协议，难以被扫描软件识别
  - 数据加密和流量混淆
  - 伪装成HTTP/DNS流量
  - 身份验证和会话管理
        """
    )
    
    # 添加全局参数
    parser.add_argument('--verbose', '-v', action='store_true', help='启用详细日志')
    
    # 创建子命令
    subparsers = parser.add_subparsers(dest='mode', help='运行模式')
    
    # GUI模式
    gui_parser = subparsers.add_parser('gui', help='启动图形界面')
    
    # 服务端模式
    server_parser = subparsers.add_parser('server', help='启动服务端')
    server_parser.add_argument('--directory', '-d', required=True, help='同步目录路径')
    server_parser.add_argument('--host', default='0.0.0.0', help='监听地址 (默认: 0.0.0.0)')
    server_parser.add_argument('--port', '-p', type=int, default=23847, help='监听端口 (默认: 23847)')
    
    # 客户端模式
    client_parser = subparsers.add_parser('client', help='启动客户端')
    client_parser.add_argument('--directory', '-d', required=True, help='同步目录路径')
    client_parser.add_argument('--server', '-s', required=True, help='服务器地址')
    client_parser.add_argument('--port', '-p', type=int, default=23847, help='服务器端口 (默认: 23847)')
    client_parser.add_argument('--client-id', default='client_001', help='客户端ID (默认: client_001)')
    
    # 解析参数
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 如果没有指定模式，默认启动GUI
    if not args.mode:
        args.mode = 'gui'
    
    # 显示启动信息
    print("=" * 60)
    print("安全文件同步工具 v1.0.0")
    print("使用私有协议的隐蔽文件同步软件")
    print("=" * 60)
    
    # 根据模式运行
    try:
        if args.mode == 'gui':
            return run_gui()
        elif args.mode == 'server':
            return run_server(args)
        elif args.mode == 'client':
            return run_client(args)
        else:
            parser.print_help()
            return 1
    except Exception as e:
        print(f"程序运行异常: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
