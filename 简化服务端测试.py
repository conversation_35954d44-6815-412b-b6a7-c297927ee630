#!/usr/bin/env python3
"""
简化版服务端测试 - 用于诊断启动问题
"""

import os
import sys
import socket
import threading
import time
import traceback
from pathlib import Path

class SimpleServer:
    """简化版服务端"""
    
    def __init__(self, sync_dir, host='127.0.0.1', port=23847):
        self.sync_dir = Path(sync_dir)
        self.host = host
        self.port = port
        self.running = False
        self.server_socket = None
        
        print(f"初始化简化服务端:")
        print(f"  同步目录: {self.sync_dir.absolute()}")
        print(f"  监听地址: {self.host}:{self.port}")
    
    def start(self):
        """启动服务端"""
        try:
            print("\n=== 启动步骤 ===")
            
            # 步骤1: 创建同步目录
            print("步骤1: 创建同步目录...")
            self.sync_dir.mkdir(parents=True, exist_ok=True)
            print("✓ 同步目录创建成功")
            
            # 步骤2: 创建socket
            print("步骤2: 创建网络socket...")
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            print("✓ Socket创建成功")
            
            # 步骤3: 绑定端口
            print("步骤3: 绑定端口...")
            self.server_socket.bind((self.host, self.port))
            print("✓ 端口绑定成功")
            
            # 步骤4: 开始监听
            print("步骤4: 开始监听...")
            self.server_socket.listen(5)
            self.server_socket.settimeout(1.0)  # 设置超时避免阻塞
            print("✓ 监听启动成功")
            
            # 步骤5: 设置运行状态
            print("步骤5: 设置运行状态...")
            self.running = True
            print("✓ 服务端启动完成")
            
            print(f"\n🎉 服务端成功启动在 {self.host}:{self.port}")
            print("等待客户端连接...")
            
            # 主循环
            connection_count = 0
            while self.running and connection_count < 3:  # 最多处理3个连接后自动退出
                try:
                    client_socket, address = self.server_socket.accept()
                    connection_count += 1
                    print(f"✓ 接收到第{connection_count}个连接: {address}")
                    
                    # 简单处理连接
                    try:
                        client_socket.send(b"Hello from SimpleServer\n")
                        client_socket.close()
                        print(f"✓ 连接{connection_count}处理完成")
                    except Exception as e:
                        print(f"✗ 处理连接{connection_count}失败: {e}")
                
                except socket.timeout:
                    # 超时是正常的，继续循环
                    print(".", end="", flush=True)
                    time.sleep(0.1)
                    continue
                except Exception as e:
                    print(f"\n✗ 接受连接失败: {e}")
                    break
            
            print(f"\n服务端运行完成，共处理 {connection_count} 个连接")
            
        except Exception as e:
            print(f"\n✗ 服务端启动失败: {e}")
            traceback.print_exc()
        finally:
            self.stop()
    
    def stop(self):
        """停止服务端"""
        print("\n正在停止服务端...")
        self.running = False
        
        if self.server_socket:
            try:
                self.server_socket.close()
                print("✓ Socket已关闭")
            except:
                pass
        
        print("✓ 服务端已停止")

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("基本功能测试")
    print("=" * 60)
    
    # 测试1: 导入检查
    print("测试1: 检查必要模块...")
    try:
        import socket
        import threading
        import time
        import pathlib
        print("✓ 所有必要模块导入成功")
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False
    
    # 测试2: 文件系统操作
    print("\n测试2: 文件系统操作...")
    try:
        test_dir = Path("test_simple_server")
        test_dir.mkdir(exist_ok=True)
        test_file = test_dir / "test.txt"
        test_file.write_text("test content")
        content = test_file.read_text()
        test_file.unlink()
        test_dir.rmdir()
        print("✓ 文件系统操作正常")
    except Exception as e:
        print(f"✗ 文件系统操作失败: {e}")
        return False
    
    # 测试3: 网络功能
    print("\n测试3: 网络功能...")
    try:
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.bind(('127.0.0.1', 0))  # 使用随机端口
        port = test_socket.getsockname()[1]
        test_socket.close()
        print(f"✓ 网络功能正常，测试端口: {port}")
    except Exception as e:
        print(f"✗ 网络功能失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("简化版服务端测试工具")
    print("用于诊断服务端启动问题")
    print()
    
    # 基本功能测试
    if not test_basic_functionality():
        print("\n❌ 基本功能测试失败，无法继续")
        input("按回车键退出...")
        return
    
    print("\n✅ 基本功能测试通过")
    
    # 获取测试参数
    sync_dir = input("\n请输入同步目录路径 (默认: ./test_sync): ").strip()
    if not sync_dir:
        sync_dir = "./test_sync"
    
    host = input("请输入监听地址 (默认: 127.0.0.1): ").strip()
    if not host:
        host = "127.0.0.1"
    
    port_str = input("请输入监听端口 (默认: 23847): ").strip()
    if not port_str:
        port = 23847
    else:
        try:
            port = int(port_str)
        except ValueError:
            print("端口号无效，使用默认端口 23847")
            port = 23847
    
    # 启动简化服务端
    print(f"\n准备启动简化服务端...")
    server = SimpleServer(sync_dir, host, port)
    
    try:
        # 在主线程中运行（便于调试）
        server.start()
    except KeyboardInterrupt:
        print("\n收到中断信号")
    except Exception as e:
        print(f"\n运行异常: {e}")
        traceback.print_exc()
    
    print("\n测试完成")
    input("按回车键退出...")

if __name__ == '__main__':
    main()
