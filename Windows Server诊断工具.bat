@echo off
chcp 65001 >nul
title Windows Server 诊断工具

echo ============================================================
echo              Windows Server 诊断工具
echo          用于诊断安全文件同步工具的兼容性问题
echo ============================================================
echo.

echo 正在收集系统信息...
echo.

echo [系统信息]
echo 操作系统: %OS%
echo 计算机名: %COMPUTERNAME%
echo 用户名: %USERNAME%
echo 系统架构: %PROCESSOR_ARCHITECTURE%
echo 当前目录: %CD%
echo.

echo [网络信息]
echo 本机IP地址:
ipconfig | findstr "IPv4"
echo.

echo [防火墙状态]
netsh advfirewall show allprofiles state
echo.

echo [端口检查]
echo 检查端口 23847 是否被占用...
netstat -an | findstr ":23847"
if %errorlevel% equ 0 (
    echo 警告: 端口 23847 已被占用
) else (
    echo 端口 23847 可用
)
echo.

echo [权限检查]
echo 检查当前目录写权限...
echo test > test_permission.tmp 2>nul
if exist test_permission.tmp (
    echo ✓ 当前目录写权限正常
    del test_permission.tmp
) else (
    echo ✗ 当前目录写权限不足
)
echo.

echo [文件检查]
if exist "dist_secure_sync\SecureSync_Debug.exe" (
    echo ✓ SecureSync_Debug.exe 存在
    for %%A in ("dist_secure_sync\SecureSync_Debug.exe") do echo   文件大小: %%~zA 字节
) else (
    echo ✗ SecureSync_Debug.exe 不存在
)

if exist "诊断工具.py" (
    echo ✓ 诊断工具.py 存在
) else (
    echo ✗ 诊断工具.py 不存在
)

if exist "简化服务端测试.py" (
    echo ✓ 简化服务端测试.py 存在
) else (
    echo ✗ 简化服务端测试.py 不存在
)
echo.

echo [Python检查]
python --version 2>nul
if %errorlevel% equ 0 (
    echo ✓ Python 可用
    echo.
    echo 运行Python诊断工具...
    echo ----------------------------------------
    python 诊断工具.py
    echo ----------------------------------------
) else (
    echo ✗ Python 不可用，跳过Python诊断
)
echo.

echo ============================================================
echo                      诊断建议
echo ============================================================
echo.

echo 如果在Windows Server上遇到启动问题，请尝试:
echo.
echo 1. 管理员权限运行
echo    - 右键点击程序，选择"以管理员身份运行"
echo.
echo 2. 防火墙设置
echo    - 控制面板 → Windows Defender 防火墙
echo    - 允许应用通过防火墙
echo    - 添加 SecureSync_Debug.exe
echo.
echo 3. 端口检查
echo    - 确保端口 23847 未被占用
echo    - 或者修改为其他端口 (如 23848, 23849)
echo.
echo 4. 目录权限
echo    - 确保程序有读写权限
echo    - 避免在系统目录运行
echo.
echo 5. 系统服务
echo    - 确保 Windows 防火墙服务正在运行
echo    - 确保网络相关服务正常
echo.
echo 6. 兼容性模式
echo    - 右键程序 → 属性 → 兼容性
echo    - 尝试 Windows 10 兼容模式
echo.
echo 7. 详细日志
echo    - 查看程序生成的 secure_sync.log 文件
echo    - 运行简化测试工具进行诊断
echo.

echo ============================================================
echo.

echo 按任意键运行简化服务端测试...
pause >nul

if exist "简化服务端测试.py" (
    echo.
    echo 启动简化服务端测试工具...
    echo ============================================================
    python 简化服务端测试.py
) else (
    echo.
    echo 简化测试工具不存在，请确保所有文件都已复制到目标系统
)

echo.
echo 诊断完成！
pause
