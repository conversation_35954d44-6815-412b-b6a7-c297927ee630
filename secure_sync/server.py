"""
安全同步服务端 - 处理客户端连接和文件同步请求
"""

import os
import json
import logging
import threading
import time
from typing import Dict, Optional
try:
    from .network import SecureServer, NetworkConnection
    from .protocol import PacketType
    from .file_monitor import SyncManager, SyncEvent
except ImportError:
    from network import SecureServer, NetworkConnection
    from protocol import PacketType
    from file_monitor import SyncManager, SyncEvent

logger = logging.getLogger(__name__)

class SyncServer:
    """文件同步服务端"""
    
    def __init__(self, sync_directory: str, host: str = '0.0.0.0', port: int = None):
        self.sync_directory = sync_directory
        self.host = host
        self.port = port
        
        # 初始化组件
        self.network_server = SecureServer(host, port)
        self.sync_manager = SyncManager(sync_directory)
        
        # 客户端管理
        self.clients: Dict[str, NetworkConnection] = {}
        self.client_sync_status: Dict[str, Dict] = {}
        
        # 设置事件回调
        self.network_server.on_client_connected = self._on_client_connected
        self.network_server.on_client_disconnected = self._on_client_disconnected
        self.network_server.on_packet_received = self._on_packet_received
        self.sync_manager.on_sync_event = self._on_sync_event
        
        # 运行状态
        self.running = False
    
    def start(self, blocking=True):
        """启动服务端"""
        try:
            logger.info("启动文件同步服务端...")

            # 启动文件监控
            self.sync_manager.start_monitoring()

            # 启动网络服务
            self.running = True
            server_thread = threading.Thread(target=self.network_server.start, daemon=True)
            server_thread.start()

            logger.info(f"服务端启动成功，监听 {self.host}:{self.port or 23847}")
            logger.info(f"同步目录: {self.sync_directory}")

            # 如果是阻塞模式（命令行模式），运行主循环
            if blocking:
                try:
                    while self.running:
                        time.sleep(1)
                except KeyboardInterrupt:
                    logger.info("收到停止信号")
                finally:
                    self.stop()

        except Exception as e:
            logger.error(f"启动服务端失败: {e}")
            if blocking:
                raise
            else:
                # 非阻塞模式下，记录错误但不抛出异常
                self.running = False
    
    def stop(self):
        """停止服务端"""
        logger.info("正在停止服务端...")
        
        self.running = False
        
        # 停止文件监控
        self.sync_manager.stop_monitoring()
        
        # 停止网络服务
        self.network_server.stop()
        
        logger.info("服务端已停止")
    
    def _on_client_connected(self, connection: NetworkConnection):
        """客户端连接事件"""
        client_id = connection.client_id
        self.clients[client_id] = connection
        self.client_sync_status[client_id] = {
            'last_sync': 0,
            'sync_in_progress': False,
            'files_synced': 0
        }
        
        logger.info(f"客户端已连接: {client_id}")
        
        # 发送初始文件列表
        self._send_file_list(connection)
    
    def _on_client_disconnected(self, connection: NetworkConnection):
        """客户端断开事件"""
        client_id = connection.client_id
        
        if client_id in self.clients:
            del self.clients[client_id]
        
        if client_id in self.client_sync_status:
            del self.client_sync_status[client_id]
        
        logger.info(f"客户端已断开: {client_id}")
    
    def _on_packet_received(self, connection: NetworkConnection, packet_type: PacketType, payload: bytes):
        """处理接收到的数据包"""
        try:
            client_id = connection.client_id
            
            if packet_type == PacketType.FILE_LIST_REQ:
                self._handle_file_list_request(connection)
            
            elif packet_type == PacketType.FILE_DATA:
                self._handle_file_data(connection, payload)
            
            elif packet_type == PacketType.FILE_ACK:
                self._handle_file_ack(connection, payload)
            
            else:
                logger.warning(f"未知数据包类型: {packet_type} from {client_id}")
        
        except Exception as e:
            logger.error(f"处理数据包失败: {e}")
    
    def _on_sync_event(self, event: SyncEvent):
        """处理文件同步事件"""
        logger.info(f"文件变化: {event.event_type} - {event.file_path}")
        
        # 通知所有连接的客户端
        for client_id, connection in self.clients.items():
            try:
                if event.event_type in ['created', 'modified']:
                    self._send_file_to_client(connection, event.file_path)
                elif event.event_type == 'deleted':
                    self._send_delete_notification(connection, event.file_path)
            except Exception as e:
                logger.error(f"通知客户端失败 {client_id}: {e}")
    
    def _send_file_list(self, connection: NetworkConnection):
        """发送文件列表给客户端"""
        try:
            file_list = self.sync_manager.get_file_list()
            
            # 序列化文件列表
            file_data = {}
            for path, info in file_list.items():
                file_data[path] = {
                    'size': info.size,
                    'mtime': info.mtime,
                    'checksum': info.checksum,
                    'is_directory': info.is_directory
                }
            
            payload = json.dumps(file_data).encode('utf-8')
            connection.send_packet(PacketType.FILE_LIST_RESP, payload)
            
            logger.debug(f"发送文件列表给 {connection.client_id}: {len(file_list)} 个文件")
        
        except Exception as e:
            logger.error(f"发送文件列表失败: {e}")
    
    def _handle_file_list_request(self, connection: NetworkConnection):
        """处理文件列表请求"""
        self._send_file_list(connection)
    
    def _handle_file_data(self, connection: NetworkConnection, payload: bytes):
        """处理文件数据"""
        try:
            if len(payload) < 8:
                return
            
            # 解析文件数据包
            file_id_len = int.from_bytes(payload[:4], 'big')
            chunk_index = int.from_bytes(payload[4:8], 'big')
            
            file_id = payload[8:8+file_id_len].decode('utf-8')
            chunk_data = payload[8+file_id_len:]
            
            # 写入文件块
            success = self.sync_manager.write_file_chunk(file_id, chunk_index, chunk_data)
            
            # 发送确认
            ack_payload = file_id.encode('utf-8') + b'\x01' if success else b'\x00'
            connection.send_packet(PacketType.FILE_ACK, ack_payload)
            
            if success:
                logger.debug(f"接收文件块: {file_id} chunk {chunk_index}")
            else:
                logger.error(f"写入文件块失败: {file_id} chunk {chunk_index}")
        
        except Exception as e:
            logger.error(f"处理文件数据失败: {e}")
    
    def _handle_file_ack(self, connection: NetworkConnection, payload: bytes):
        """处理文件确认"""
        try:
            if len(payload) < 1:
                return
            
            file_id = payload[:-1].decode('utf-8')
            success = payload[-1] == 1
            
            if success:
                logger.debug(f"文件传输确认: {file_id}")
            else:
                logger.warning(f"文件传输失败: {file_id}")
        
        except Exception as e:
            logger.error(f"处理文件确认失败: {e}")
    
    def _send_file_to_client(self, connection: NetworkConnection, file_path: str):
        """发送文件给客户端"""
        try:
            chunks = self.sync_manager.get_file_chunks(file_path)
            
            for chunk_index, chunk_data in enumerate(chunks):
                packet = connection.protocol.create_file_data_packet(
                    file_path, chunk_index, chunk_data
                )
                connection.send_queue.put(packet)
            
            logger.debug(f"发送文件给 {connection.client_id}: {file_path} ({len(chunks)} 块)")
        
        except Exception as e:
            logger.error(f"发送文件失败: {e}")
    
    def _send_delete_notification(self, connection: NetworkConnection, file_path: str):
        """发送删除通知给客户端"""
        try:
            # 使用特殊的删除标记
            payload = b'DELETE:' + file_path.encode('utf-8')
            connection.send_packet(PacketType.FILE_DATA, payload)
            
            logger.debug(f"发送删除通知给 {connection.client_id}: {file_path}")
        
        except Exception as e:
            logger.error(f"发送删除通知失败: {e}")
    
    def get_status(self) -> Dict:
        """获取服务端状态"""
        file_list = self.sync_manager.get_file_list()
        
        return {
            'running': self.running,
            'sync_directory': self.sync_directory,
            'total_files': len(file_list),
            'connected_clients': len(self.clients),
            'clients': list(self.clients.keys()),
            'client_status': self.client_sync_status.copy()
        }

def main():
    """服务端主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='安全文件同步服务端')
    parser.add_argument('--directory', '-d', required=True, help='同步目录路径')
    parser.add_argument('--host', default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=23847, help='监听端口')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细日志')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 检查同步目录
    if not os.path.exists(args.directory):
        logger.error(f"同步目录不存在: {args.directory}")
        return
    
    # 启动服务端
    server = SyncServer(args.directory, args.host, args.port)
    
    try:
        server.start()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止...")
    except Exception as e:
        logger.error(f"服务端运行异常: {e}")
    finally:
        server.stop()

if __name__ == '__main__':
    main()
