"""
安全文件同步系统
一个使用私有协议和非常用端口的隐蔽文件同步软件
"""

__version__ = "1.0.0"
__author__ = "SecureSync Team"
__description__ = "隐蔽的局域网文件同步软件"

from .protocol import SecureProtocol, PacketType, PROTOCOL_CONFIG
from .network import SecureServer, SecureClient, NetworkConnection
from .file_monitor import SyncManager, FileMonitor, SyncEvent, FileInfo
from .server import SyncServer
from .client import SyncClient
from .security import SecurityManager, AdvancedCrypto, AuthenticationManager, TrafficObfuscator
from .gui import SyncGUI

__all__ = [
    'SecureProtocol',
    'PacketType', 
    'PROTOCOL_CONFIG',
    'SecureServer',
    'SecureClient',
    'NetworkConnection',
    'SyncManager',
    'FileMonitor',
    'SyncEvent',
    'FileInfo',
    'SyncServer',
    'SyncClient',
    'SecurityManager',
    'AdvancedCrypto',
    'AuthenticationManager',
    'TrafficObfuscator',
    'SyncGUI'
]
