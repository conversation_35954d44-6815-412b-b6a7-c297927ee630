"""
文件监控系统 - 实现实时文件变化监控和同步
支持增量同步、文件校验、冲突处理等功能
"""

import os
import hashlib
import time
import threading
import logging
from typing import Dict, Set, Optional, Callable, List, Tuple
from dataclasses import dataclass
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent

logger = logging.getLogger(__name__)

@dataclass
class FileInfo:
    """文件信息"""
    path: str
    size: int
    mtime: float
    checksum: str
    is_directory: bool = False

@dataclass
class SyncEvent:
    """同步事件"""
    event_type: str  # 'created', 'modified', 'deleted', 'moved'
    file_path: str
    old_path: Optional[str] = None
    file_info: Optional[FileInfo] = None
    timestamp: float = 0.0

class FileMonitor(FileSystemEventHandler):
    """文件系统监控器"""
    
    def __init__(self, sync_manager):
        super().__init__()
        self.sync_manager = sync_manager
        self.ignore_patterns = {'.tmp', '.swp', '~', '.lock', '.sync'}
        
    def should_ignore(self, path: str) -> bool:
        """检查是否应该忽略文件"""
        file_name = os.path.basename(path)
        
        # 忽略临时文件和系统文件
        for pattern in self.ignore_patterns:
            if pattern in file_name:
                return True
        
        # 忽略隐藏文件
        if file_name.startswith('.'):
            return True
            
        return False
    
    def on_created(self, event: FileSystemEvent):
        """文件创建事件"""
        if not self.should_ignore(event.src_path):
            self.sync_manager.on_file_event('created', event.src_path)
    
    def on_modified(self, event: FileSystemEvent):
        """文件修改事件"""
        if not event.is_directory and not self.should_ignore(event.src_path):
            self.sync_manager.on_file_event('modified', event.src_path)
    
    def on_deleted(self, event: FileSystemEvent):
        """文件删除事件"""
        if not self.should_ignore(event.src_path):
            self.sync_manager.on_file_event('deleted', event.src_path)
    
    def on_moved(self, event: FileSystemEvent):
        """文件移动事件"""
        if not self.should_ignore(event.src_path):
            self.sync_manager.on_file_event('moved', event.src_path, event.dest_path)

class SyncManager:
    """同步管理器"""
    
    def __init__(self, sync_directory: str):
        self.sync_directory = Path(sync_directory).resolve()
        self.file_index: Dict[str, FileInfo] = {}
        self.pending_events: List[SyncEvent] = []
        self.observer: Optional[Observer] = None
        self.running = False
        self.lock = threading.RLock()
        
        # 事件回调
        self.on_sync_event: Optional[Callable[[SyncEvent], None]] = None
        
        # 确保同步目录存在
        self.sync_directory.mkdir(parents=True, exist_ok=True)
        
        # 初始化文件索引
        self._build_file_index()
    
    def start_monitoring(self):
        """开始监控文件变化"""
        if self.running:
            return
        
        self.observer = Observer()
        file_monitor = FileMonitor(self)
        self.observer.schedule(file_monitor, str(self.sync_directory), recursive=True)
        
        self.observer.start()
        self.running = True
        
        # 启动事件处理线程
        event_thread = threading.Thread(target=self._process_events, daemon=True)
        event_thread.start()
        
        logger.info(f"开始监控目录: {self.sync_directory}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.observer = None
        
        logger.info("停止文件监控")
    
    def _build_file_index(self):
        """构建文件索引"""
        logger.info("构建文件索引...")
        
        with self.lock:
            self.file_index.clear()
            
            for root, dirs, files in os.walk(self.sync_directory):
                # 处理目录
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    rel_path = os.path.relpath(dir_path, self.sync_directory)
                    
                    self.file_index[rel_path] = FileInfo(
                        path=rel_path,
                        size=0,
                        mtime=os.path.getmtime(dir_path),
                        checksum="",
                        is_directory=True
                    )
                
                # 处理文件
                for file_name in files:
                    file_path = os.path.join(root, file_name)
                    rel_path = os.path.relpath(file_path, self.sync_directory)
                    
                    try:
                        file_info = self._get_file_info(file_path, rel_path)
                        self.file_index[rel_path] = file_info
                    except Exception as e:
                        logger.warning(f"无法获取文件信息: {file_path}, {e}")
        
        logger.info(f"文件索引构建完成，共 {len(self.file_index)} 个项目")
    
    def _get_file_info(self, full_path: str, rel_path: str) -> FileInfo:
        """获取文件信息"""
        stat = os.stat(full_path)
        
        # 计算文件校验和
        checksum = ""
        if os.path.isfile(full_path):
            checksum = self._calculate_checksum(full_path)
        
        return FileInfo(
            path=rel_path,
            size=stat.st_size,
            mtime=stat.st_mtime,
            checksum=checksum,
            is_directory=os.path.isdir(full_path)
        )
    
    def _calculate_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.warning(f"计算校验和失败: {file_path}, {e}")
            return ""
    
    def on_file_event(self, event_type: str, src_path: str, dest_path: str = None):
        """处理文件事件"""
        try:
            rel_path = os.path.relpath(src_path, self.sync_directory)
            
            # 创建同步事件
            sync_event = SyncEvent(
                event_type=event_type,
                file_path=rel_path,
                old_path=os.path.relpath(dest_path, self.sync_directory) if dest_path else None,
                timestamp=time.time()
            )
            
            # 获取文件信息
            if event_type != 'deleted' and os.path.exists(src_path):
                sync_event.file_info = self._get_file_info(src_path, rel_path)
            
            with self.lock:
                self.pending_events.append(sync_event)
            
            logger.debug(f"文件事件: {event_type} - {rel_path}")
            
        except Exception as e:
            logger.error(f"处理文件事件失败: {e}")
    
    def _process_events(self):
        """处理待处理的事件"""
        while self.running:
            try:
                events_to_process = []
                
                with self.lock:
                    if self.pending_events:
                        events_to_process = self.pending_events.copy()
                        self.pending_events.clear()
                
                for event in events_to_process:
                    self._handle_sync_event(event)
                
                time.sleep(0.5)  # 避免过于频繁的处理
                
            except Exception as e:
                logger.error(f"处理事件失败: {e}")
    
    def _handle_sync_event(self, event: SyncEvent):
        """处理单个同步事件"""
        try:
            with self.lock:
                if event.event_type == 'created':
                    if event.file_info:
                        self.file_index[event.file_path] = event.file_info
                
                elif event.event_type == 'modified':
                    if event.file_info:
                        old_info = self.file_index.get(event.file_path)
                        
                        # 检查是否真的发生了变化
                        if old_info and old_info.checksum == event.file_info.checksum:
                            return  # 没有实际变化，忽略
                        
                        self.file_index[event.file_path] = event.file_info
                
                elif event.event_type == 'deleted':
                    if event.file_path in self.file_index:
                        del self.file_index[event.file_path]
                
                elif event.event_type == 'moved':
                    if event.file_path in self.file_index:
                        old_info = self.file_index[event.file_path]
                        del self.file_index[event.file_path]
                        
                        if event.old_path:
                            old_info.path = event.old_path
                            self.file_index[event.old_path] = old_info
            
            # 触发同步事件回调
            if self.on_sync_event:
                self.on_sync_event(event)
            
        except Exception as e:
            logger.error(f"处理同步事件失败: {e}")
    
    def get_file_list(self) -> Dict[str, FileInfo]:
        """获取文件列表"""
        with self.lock:
            return self.file_index.copy()
    
    def get_file_changes(self, remote_file_list: Dict[str, FileInfo]) -> Tuple[List[str], List[str], List[str]]:
        """比较文件列表，返回需要同步的文件"""
        with self.lock:
            local_files = set(self.file_index.keys())
            remote_files = set(remote_file_list.keys())
            
            # 需要上传的文件（本地有，远程没有或不同）
            to_upload = []
            for file_path in local_files:
                local_info = self.file_index[file_path]
                remote_info = remote_file_list.get(file_path)
                
                if not remote_info or local_info.checksum != remote_info.checksum:
                    to_upload.append(file_path)
            
            # 需要下载的文件（远程有，本地没有或不同）
            to_download = []
            for file_path in remote_files:
                remote_info = remote_file_list[file_path]
                local_info = self.file_index.get(file_path)
                
                if not local_info or remote_info.checksum != local_info.checksum:
                    to_download.append(file_path)
            
            # 需要删除的文件（本地有，远程没有）
            to_delete = list(local_files - remote_files)
            
            return to_upload, to_download, to_delete
    
    def get_file_chunks(self, file_path: str, chunk_size: int = 32768) -> List[bytes]:
        """将文件分块读取"""
        full_path = self.sync_directory / file_path
        chunks = []
        
        try:
            with open(full_path, 'rb') as f:
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    chunks.append(chunk)
        except Exception as e:
            logger.error(f"读取文件块失败: {file_path}, {e}")
            
        return chunks
    
    def write_file_chunk(self, file_path: str, chunk_index: int, chunk_data: bytes) -> bool:
        """写入文件块"""
        try:
            full_path = self.sync_directory / file_path
            
            # 确保目录存在
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件块（简化版本，实际应该支持断点续传）
            mode = 'ab' if chunk_index > 0 else 'wb'
            with open(full_path, mode) as f:
                f.write(chunk_data)
            
            return True
            
        except Exception as e:
            logger.error(f"写入文件块失败: {file_path}, {e}")
            return False
    
    def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            full_path = self.sync_directory / file_path
            
            if full_path.is_file():
                full_path.unlink()
            elif full_path.is_dir():
                full_path.rmdir()  # 只删除空目录
            
            # 更新索引
            with self.lock:
                if file_path in self.file_index:
                    del self.file_index[file_path]
            
            return True
            
        except Exception as e:
            logger.error(f"删除文件失败: {file_path}, {e}")
            return False
