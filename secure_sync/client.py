"""
安全同步客户端 - 连接服务端并进行文件同步
"""

import os
import json
import logging
import threading
import time
from typing import Dict, Optional, Set
try:
    from .network import SecureClient
    from .protocol import PacketType
    from .file_monitor import Sync<PERSON>anager, SyncEvent, FileInfo
except ImportError:
    from network import SecureClient
    from protocol import PacketType
    from file_monitor import Sync<PERSON>anager, SyncEvent, FileInfo

logger = logging.getLogger(__name__)

class SyncClient:
    """文件同步客户端"""
    
    def __init__(self, client_id: str, sync_directory: str):
        self.client_id = client_id
        self.sync_directory = sync_directory
        
        # 初始化组件
        self.network_client = SecureClient(client_id)
        self.sync_manager = SyncManager(sync_directory)
        
        # 同步状态
        self.connected = False
        self.sync_in_progress = False
        self.remote_file_list: Dict[str, FileInfo] = {}
        self.pending_uploads: Set[str] = set()
        self.pending_downloads: Set[str] = set()
        
        # 设置事件回调
        self.network_client.on_connected = self._on_connected
        self.network_client.on_disconnected = self._on_disconnected
        self.network_client.on_packet_received = self._on_packet_received
        self.sync_manager.on_sync_event = self._on_sync_event
        
        # 运行状态
        self.running = False
        self.auto_sync = True
    
    def connect(self, server_host: str, server_port: int = None) -> bool:
        """连接到服务端"""
        try:
            logger.info(f"连接到服务端: {server_host}:{server_port or 23847}")
            
            success = self.network_client.connect(server_host, server_port)
            if success:
                # 进行身份认证
                self._authenticate()
                
                # 启动文件监控
                self.sync_manager.start_monitoring()
                
                self.running = True
                
                # 启动同步工作线程
                sync_thread = threading.Thread(target=self._sync_worker, daemon=True)
                sync_thread.start()
                
                logger.info("客户端连接成功")
                return True
            else:
                logger.error("连接服务端失败")
                return False
        
        except Exception as e:
            logger.error(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        logger.info("断开连接...")
        
        self.running = False
        self.connected = False
        
        # 停止文件监控
        self.sync_manager.stop_monitoring()
        
        # 断开网络连接
        self.network_client.disconnect()
        
        logger.info("客户端已断开")
    
    def _authenticate(self):
        """身份认证"""
        try:
            # 简化的认证过程
            username = "sync_user"
            password_hash = "secure_hash_2025"
            
            auth_packet = self.network_client.connection.protocol.create_auth_request(
                username, password_hash
            )
            self.network_client.connection.send_queue.put(auth_packet)
            
            logger.debug("发送认证请求")
        
        except Exception as e:
            logger.error(f"认证失败: {e}")
    
    def _on_connected(self):
        """连接成功事件"""
        self.connected = True
        logger.info("网络连接已建立")
    
    def _on_disconnected(self):
        """连接断开事件"""
        self.connected = False
        logger.info("网络连接已断开")
        
        if self.running:
            # 尝试重连
            threading.Thread(target=self._reconnect, daemon=True).start()
    
    def _on_packet_received(self, packet_type: PacketType, payload: bytes):
        """处理接收到的数据包"""
        try:
            if packet_type == PacketType.AUTH_ACK:
                self._handle_auth_response(payload)
            
            elif packet_type == PacketType.FILE_LIST_RESP:
                self._handle_file_list_response(payload)
            
            elif packet_type == PacketType.FILE_DATA:
                self._handle_file_data(payload)
            
            elif packet_type == PacketType.FILE_ACK:
                self._handle_file_ack(payload)
            
            else:
                logger.warning(f"未知数据包类型: {packet_type}")
        
        except Exception as e:
            logger.error(f"处理数据包失败: {e}")
    
    def _on_sync_event(self, event: SyncEvent):
        """处理本地文件变化事件"""
        if not self.connected or not self.auto_sync:
            return
        
        logger.info(f"本地文件变化: {event.event_type} - {event.file_path}")
        
        if event.event_type in ['created', 'modified']:
            self.pending_uploads.add(event.file_path)
        elif event.event_type == 'deleted':
            self._send_delete_notification(event.file_path)
    
    def _handle_auth_response(self, payload: bytes):
        """处理认证响应"""
        if len(payload) > 0 and payload[0] == 1:
            logger.info("认证成功")
            # 请求文件列表
            self._request_file_list()
        else:
            logger.error("认证失败")
            self.disconnect()
    
    def _handle_file_list_response(self, payload: bytes):
        """处理文件列表响应"""
        try:
            file_data = json.loads(payload.decode('utf-8'))
            
            # 转换为FileInfo对象
            self.remote_file_list.clear()
            for path, info in file_data.items():
                self.remote_file_list[path] = FileInfo(
                    path=path,
                    size=info['size'],
                    mtime=info['mtime'],
                    checksum=info['checksum'],
                    is_directory=info['is_directory']
                )
            
            logger.info(f"收到文件列表: {len(self.remote_file_list)} 个文件")
            
            # 开始同步
            self._start_sync()
        
        except Exception as e:
            logger.error(f"处理文件列表失败: {e}")
    
    def _handle_file_data(self, payload: bytes):
        """处理文件数据"""
        try:
            # 检查是否是删除通知
            if payload.startswith(b'DELETE:'):
                file_path = payload[7:].decode('utf-8')
                self._handle_delete_notification(file_path)
                return
            
            if len(payload) < 8:
                return
            
            # 解析文件数据包
            file_id_len = int.from_bytes(payload[:4], 'big')
            chunk_index = int.from_bytes(payload[4:8], 'big')
            
            file_id = payload[8:8+file_id_len].decode('utf-8')
            chunk_data = payload[8+file_id_len:]
            
            # 写入文件块
            success = self.sync_manager.write_file_chunk(file_id, chunk_index, chunk_data)
            
            # 发送确认
            ack_payload = file_id.encode('utf-8') + (b'\x01' if success else b'\x00')
            self.network_client.send_packet(PacketType.FILE_ACK, ack_payload)
            
            if success:
                logger.debug(f"接收文件块: {file_id} chunk {chunk_index}")
                
                # 从待下载列表中移除
                if file_id in self.pending_downloads:
                    self.pending_downloads.discard(file_id)
            else:
                logger.error(f"写入文件块失败: {file_id} chunk {chunk_index}")
        
        except Exception as e:
            logger.error(f"处理文件数据失败: {e}")
    
    def _handle_file_ack(self, payload: bytes):
        """处理文件确认"""
        try:
            if len(payload) < 1:
                return
            
            file_id = payload[:-1].decode('utf-8')
            success = payload[-1] == 1
            
            if success:
                logger.debug(f"文件上传确认: {file_id}")
                # 从待上传列表中移除
                self.pending_uploads.discard(file_id)
            else:
                logger.warning(f"文件上传失败: {file_id}")
        
        except Exception as e:
            logger.error(f"处理文件确认失败: {e}")
    
    def _handle_delete_notification(self, file_path: str):
        """处理删除通知"""
        try:
            success = self.sync_manager.delete_file(file_path)
            if success:
                logger.info(f"删除文件: {file_path}")
            else:
                logger.warning(f"删除文件失败: {file_path}")
        
        except Exception as e:
            logger.error(f"处理删除通知失败: {e}")
    
    def _request_file_list(self):
        """请求文件列表"""
        self.network_client.send_packet(PacketType.FILE_LIST_REQ)
    
    def _start_sync(self):
        """开始同步"""
        if self.sync_in_progress:
            return
        
        self.sync_in_progress = True
        
        try:
            # 比较文件列表
            to_upload, to_download, to_delete = self.sync_manager.get_file_changes(
                self.remote_file_list
            )
            
            logger.info(f"同步计划: 上传 {len(to_upload)}, 下载 {len(to_download)}, 删除 {len(to_delete)}")
            
            # 添加到待处理队列
            self.pending_uploads.update(to_upload)
            self.pending_downloads.update(to_download)
            
            # 处理删除
            for file_path in to_delete:
                self.sync_manager.delete_file(file_path)
        
        except Exception as e:
            logger.error(f"开始同步失败: {e}")
        finally:
            self.sync_in_progress = False
    
    def _sync_worker(self):
        """同步工作线程"""
        while self.running:
            try:
                if self.connected and not self.sync_in_progress:
                    # 处理待上传文件
                    if self.pending_uploads:
                        file_path = self.pending_uploads.pop()
                        self._upload_file(file_path)
                
                time.sleep(1)  # 避免过于频繁的处理
            
            except Exception as e:
                logger.error(f"同步工作线程异常: {e}")
    
    def _upload_file(self, file_path: str):
        """上传文件"""
        try:
            chunks = self.sync_manager.get_file_chunks(file_path)
            
            for chunk_index, chunk_data in enumerate(chunks):
                packet = self.network_client.connection.protocol.create_file_data_packet(
                    file_path, chunk_index, chunk_data
                )
                self.network_client.connection.send_queue.put(packet)
            
            logger.debug(f"上传文件: {file_path} ({len(chunks)} 块)")
        
        except Exception as e:
            logger.error(f"上传文件失败: {e}")
    
    def _send_delete_notification(self, file_path: str):
        """发送删除通知"""
        try:
            payload = b'DELETE:' + file_path.encode('utf-8')
            self.network_client.send_packet(PacketType.FILE_DATA, payload)
            
            logger.debug(f"发送删除通知: {file_path}")
        
        except Exception as e:
            logger.error(f"发送删除通知失败: {e}")
    
    def _reconnect(self):
        """重连逻辑"""
        retry_count = 0
        max_retries = 10
        
        while self.running and not self.connected and retry_count < max_retries:
            retry_count += 1
            logger.info(f"尝试重连 ({retry_count}/{max_retries})...")
            
            time.sleep(5 * retry_count)  # 递增延迟
            
            # 这里需要保存服务器地址信息以便重连
            # 简化版本暂时跳过自动重连
            break
    
    def get_status(self) -> Dict:
        """获取客户端状态"""
        local_files = self.sync_manager.get_file_list()
        
        return {
            'client_id': self.client_id,
            'connected': self.connected,
            'sync_directory': self.sync_directory,
            'local_files': len(local_files),
            'remote_files': len(self.remote_file_list),
            'pending_uploads': len(self.pending_uploads),
            'pending_downloads': len(self.pending_downloads),
            'sync_in_progress': self.sync_in_progress,
            'auto_sync': self.auto_sync
        }

def main():
    """客户端主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='安全文件同步客户端')
    parser.add_argument('--directory', '-d', required=True, help='同步目录路径')
    parser.add_argument('--server', '-s', required=True, help='服务器地址')
    parser.add_argument('--port', '-p', type=int, default=23847, help='服务器端口')
    parser.add_argument('--client-id', default='client_001', help='客户端ID')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细日志')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 检查同步目录
    if not os.path.exists(args.directory):
        os.makedirs(args.directory, exist_ok=True)
        logger.info(f"创建同步目录: {args.directory}")
    
    # 启动客户端
    client = SyncClient(args.client_id, args.directory)
    
    try:
        if client.connect(args.server, args.port):
            logger.info("客户端运行中，按 Ctrl+C 停止...")
            while client.running:
                time.sleep(1)
        else:
            logger.error("无法连接到服务器")
    
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止...")
    except Exception as e:
        logger.error(f"客户端运行异常: {e}")
    finally:
        client.disconnect()

if __name__ == '__main__':
    main()
