"""
GUI管理界面 - 文件同步软件的图形用户界面
"""

import os
import json
import threading
import time
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from typing import Optional, Dict, Any
import logging

try:
    from .server import SyncServer
    from .client import SyncClient
except ImportError:
    from server import SyncServer
    from client import SyncClient

logger = logging.getLogger(__name__)

class SyncGUI:
    """文件同步GUI主界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("安全文件同步工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 应用状态
        self.server: Optional[SyncServer] = None
        self.client: Optional[SyncClient] = None
        self.server_thread: Optional[threading.Thread] = None
        self.config_file = "sync_config.json"
        
        # 配置数据
        self.config = self.load_config()
        
        # 创建界面
        self.create_widgets()
        self.load_settings()
        
        # 启动状态更新
        self.update_status()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建笔记本控件
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 服务端标签页
        self.server_frame = ttk.Frame(notebook)
        notebook.add(self.server_frame, text="服务端")
        self.create_server_tab()
        
        # 客户端标签页
        self.client_frame = ttk.Frame(notebook)
        notebook.add(self.client_frame, text="客户端")
        self.create_client_tab()
        
        # 设置标签页
        self.settings_frame = ttk.Frame(notebook)
        notebook.add(self.settings_frame, text="设置")
        self.create_settings_tab()
        
        # 日志标签页
        self.log_frame = ttk.Frame(notebook)
        notebook.add(self.log_frame, text="日志")
        self.create_log_tab()
    
    def create_server_tab(self):
        """创建服务端标签页"""
        # 服务端配置区域
        config_frame = ttk.LabelFrame(self.server_frame, text="服务端配置", padding="10")
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 同步目录
        ttk.Label(config_frame, text="同步目录:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.server_dir_var = tk.StringVar()
        dir_frame = ttk.Frame(config_frame)
        dir_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        dir_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(dir_frame, textvariable=self.server_dir_var, width=50).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(dir_frame, text="浏览", command=self.browse_server_dir).grid(row=0, column=1, padx=(5, 0))
        
        # 监听地址
        ttk.Label(config_frame, text="监听地址:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.server_host_var = tk.StringVar(value="0.0.0.0")
        ttk.Entry(config_frame, textvariable=self.server_host_var, width=20).grid(row=1, column=1, sticky=tk.W, pady=2)
        
        # 监听端口
        ttk.Label(config_frame, text="监听端口:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.server_port_var = tk.StringVar(value="23847")
        ttk.Entry(config_frame, textvariable=self.server_port_var, width=10).grid(row=2, column=1, sticky=tk.W, pady=2)
        
        config_frame.columnconfigure(1, weight=1)
        
        # 服务端控制区域
        control_frame = ttk.Frame(self.server_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.server_start_btn = ttk.Button(control_frame, text="启动服务端", command=self.start_server)
        self.server_start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.server_stop_btn = ttk.Button(control_frame, text="停止服务端", command=self.stop_server, state=tk.DISABLED)
        self.server_stop_btn.pack(side=tk.LEFT)
        
        # 服务端状态区域
        status_frame = ttk.LabelFrame(self.server_frame, text="服务端状态", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.server_status_text = tk.Text(status_frame, height=15, state=tk.DISABLED)
        scrollbar_s = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.server_status_text.yview)
        self.server_status_text.configure(yscrollcommand=scrollbar_s.set)
        
        self.server_status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_s.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_client_tab(self):
        """创建客户端标签页"""
        # 客户端配置区域
        config_frame = ttk.LabelFrame(self.client_frame, text="客户端配置", padding="10")
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 同步目录
        ttk.Label(config_frame, text="同步目录:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.client_dir_var = tk.StringVar()
        dir_frame = ttk.Frame(config_frame)
        dir_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        dir_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(dir_frame, textvariable=self.client_dir_var, width=50).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(dir_frame, text="浏览", command=self.browse_client_dir).grid(row=0, column=1, padx=(5, 0))
        
        # 服务器地址
        ttk.Label(config_frame, text="服务器地址:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.client_host_var = tk.StringVar(value="127.0.0.1")
        ttk.Entry(config_frame, textvariable=self.client_host_var, width=20).grid(row=1, column=1, sticky=tk.W, pady=2)
        
        # 服务器端口
        ttk.Label(config_frame, text="服务器端口:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.client_port_var = tk.StringVar(value="23847")
        ttk.Entry(config_frame, textvariable=self.client_port_var, width=10).grid(row=2, column=1, sticky=tk.W, pady=2)
        
        # 客户端ID
        ttk.Label(config_frame, text="客户端ID:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.client_id_var = tk.StringVar(value="client_001")
        ttk.Entry(config_frame, textvariable=self.client_id_var, width=20).grid(row=3, column=1, sticky=tk.W, pady=2)
        
        config_frame.columnconfigure(1, weight=1)
        
        # 客户端控制区域
        control_frame = ttk.Frame(self.client_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.client_connect_btn = ttk.Button(control_frame, text="连接服务器", command=self.connect_client)
        self.client_connect_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.client_disconnect_btn = ttk.Button(control_frame, text="断开连接", command=self.disconnect_client, state=tk.DISABLED)
        self.client_disconnect_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.auto_sync_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="自动同步", variable=self.auto_sync_var, command=self.toggle_auto_sync).pack(side=tk.LEFT)
        
        # 客户端状态区域
        status_frame = ttk.LabelFrame(self.client_frame, text="客户端状态", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.client_status_text = tk.Text(status_frame, height=15, state=tk.DISABLED)
        scrollbar_c = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.client_status_text.yview)
        self.client_status_text.configure(yscrollcommand=scrollbar_c.set)
        
        self.client_status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_c.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_settings_tab(self):
        """创建设置标签页"""
        # 安全设置
        security_frame = ttk.LabelFrame(self.settings_frame, text="安全设置", padding="10")
        security_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(security_frame, text="主密钥:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.master_key_var = tk.StringVar(value="SecureSync2025_MasterKey")
        ttk.Entry(security_frame, textvariable=self.master_key_var, width=40, show="*").grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Label(security_frame, text="用户名:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.username_var = tk.StringVar(value="sync_user")
        ttk.Entry(security_frame, textvariable=self.username_var, width=20).grid(row=1, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(security_frame, text="密码:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.password_var = tk.StringVar(value="SecurePassword2025!")
        ttk.Entry(security_frame, textvariable=self.password_var, width=20, show="*").grid(row=2, column=1, sticky=tk.W, pady=2)
        
        security_frame.columnconfigure(1, weight=1)
        
        # 高级设置
        advanced_frame = ttk.LabelFrame(self.settings_frame, text="高级设置", padding="10")
        advanced_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.enable_encryption_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(advanced_frame, text="启用数据加密", variable=self.enable_encryption_var).pack(anchor=tk.W)
        
        self.enable_obfuscation_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(advanced_frame, text="启用流量混淆", variable=self.enable_obfuscation_var).pack(anchor=tk.W)
        
        self.verbose_logging_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(advanced_frame, text="详细日志", variable=self.verbose_logging_var, command=self.toggle_logging).pack(anchor=tk.W)
        
        # 保存设置按钮
        ttk.Button(self.settings_frame, text="保存设置", command=self.save_settings).pack(pady=10)
    
    def create_log_tab(self):
        """创建日志标签页"""
        log_frame = ttk.Frame(self.log_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = tk.Text(log_frame, state=tk.DISABLED)
        scrollbar_log = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar_log.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_log.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 清除日志按钮
        ttk.Button(self.log_frame, text="清除日志", command=self.clear_log).pack(pady=5)
    
    def browse_server_dir(self):
        """浏览服务端目录"""
        directory = filedialog.askdirectory(title="选择服务端同步目录")
        if directory:
            self.server_dir_var.set(directory)
    
    def browse_client_dir(self):
        """浏览客户端目录"""
        directory = filedialog.askdirectory(title="选择客户端同步目录")
        if directory:
            self.client_dir_var.set(directory)
    
    def start_server(self):
        """启动服务端"""
        try:
            sync_dir = self.server_dir_var.get().strip()
            if not sync_dir:
                messagebox.showerror("错误", "请选择同步目录")
                return
            
            if not os.path.exists(sync_dir):
                os.makedirs(sync_dir, exist_ok=True)
            
            host = self.server_host_var.get().strip()
            port = int(self.server_port_var.get().strip())
            
            self.server = SyncServer(sync_dir, host, port)
            
            # 在新线程中启动服务端
            self.server_thread = threading.Thread(target=self.server.start, daemon=True)
            self.server_thread.start()
            
            # 更新界面状态
            self.server_start_btn.config(state=tk.DISABLED)
            self.server_stop_btn.config(state=tk.NORMAL)
            
            self.log_message(f"服务端已启动: {host}:{port}")
            
        except Exception as e:
            messagebox.showerror("错误", f"启动服务端失败: {str(e)}")
            self.log_message(f"启动服务端失败: {str(e)}")
    
    def stop_server(self):
        """停止服务端"""
        try:
            if self.server:
                self.server.stop()
                self.server = None
            
            # 更新界面状态
            self.server_start_btn.config(state=tk.NORMAL)
            self.server_stop_btn.config(state=tk.DISABLED)
            
            self.log_message("服务端已停止")
            
        except Exception as e:
            messagebox.showerror("错误", f"停止服务端失败: {str(e)}")
            self.log_message(f"停止服务端失败: {str(e)}")
    
    def connect_client(self):
        """连接客户端"""
        try:
            sync_dir = self.client_dir_var.get().strip()
            if not sync_dir:
                messagebox.showerror("错误", "请选择同步目录")
                return
            
            if not os.path.exists(sync_dir):
                os.makedirs(sync_dir, exist_ok=True)
            
            client_id = self.client_id_var.get().strip()
            host = self.client_host_var.get().strip()
            port = int(self.client_port_var.get().strip())
            
            self.client = SyncClient(client_id, sync_dir)
            
            if self.client.connect(host, port):
                # 更新界面状态
                self.client_connect_btn.config(state=tk.DISABLED)
                self.client_disconnect_btn.config(state=tk.NORMAL)
                
                self.log_message(f"客户端已连接: {host}:{port}")
            else:
                messagebox.showerror("错误", "连接服务器失败")
                self.log_message("连接服务器失败")
                self.client = None
            
        except Exception as e:
            messagebox.showerror("错误", f"连接失败: {str(e)}")
            self.log_message(f"连接失败: {str(e)}")
    
    def disconnect_client(self):
        """断开客户端连接"""
        try:
            if self.client:
                self.client.disconnect()
                self.client = None
            
            # 更新界面状态
            self.client_connect_btn.config(state=tk.NORMAL)
            self.client_disconnect_btn.config(state=tk.DISABLED)
            
            self.log_message("客户端已断开连接")
            
        except Exception as e:
            messagebox.showerror("错误", f"断开连接失败: {str(e)}")
            self.log_message(f"断开连接失败: {str(e)}")
    
    def toggle_auto_sync(self):
        """切换自动同步"""
        if self.client:
            self.client.auto_sync = self.auto_sync_var.get()
            status = "启用" if self.client.auto_sync else "禁用"
            self.log_message(f"自动同步已{status}")
    
    def toggle_logging(self):
        """切换日志级别"""
        level = logging.DEBUG if self.verbose_logging_var.get() else logging.INFO
        logging.getLogger().setLevel(level)
        self.log_message(f"日志级别已设置为: {'DEBUG' if self.verbose_logging_var.get() else 'INFO'}")
    
    def save_settings(self):
        """保存设置"""
        self.config.update({
            'server_dir': self.server_dir_var.get(),
            'server_host': self.server_host_var.get(),
            'server_port': self.server_port_var.get(),
            'client_dir': self.client_dir_var.get(),
            'client_host': self.client_host_var.get(),
            'client_port': self.client_port_var.get(),
            'client_id': self.client_id_var.get(),
            'master_key': self.master_key_var.get(),
            'username': self.username_var.get(),
            'password': self.password_var.get(),
            'enable_encryption': self.enable_encryption_var.get(),
            'enable_obfuscation': self.enable_obfuscation_var.get(),
            'verbose_logging': self.verbose_logging_var.get(),
            'auto_sync': self.auto_sync_var.get()
        })
        
        self.save_config()
        messagebox.showinfo("成功", "设置已保存")
        self.log_message("设置已保存")
    
    def load_settings(self):
        """加载设置"""
        self.server_dir_var.set(self.config.get('server_dir', ''))
        self.server_host_var.set(self.config.get('server_host', '0.0.0.0'))
        self.server_port_var.set(self.config.get('server_port', '23847'))
        self.client_dir_var.set(self.config.get('client_dir', ''))
        self.client_host_var.set(self.config.get('client_host', '127.0.0.1'))
        self.client_port_var.set(self.config.get('client_port', '23847'))
        self.client_id_var.set(self.config.get('client_id', 'client_001'))
        self.master_key_var.set(self.config.get('master_key', 'SecureSync2025_MasterKey'))
        self.username_var.set(self.config.get('username', 'sync_user'))
        self.password_var.set(self.config.get('password', 'SecurePassword2025!'))
        self.enable_encryption_var.set(self.config.get('enable_encryption', True))
        self.enable_obfuscation_var.set(self.config.get('enable_obfuscation', True))
        self.verbose_logging_var.set(self.config.get('verbose_logging', False))
        self.auto_sync_var.set(self.config.get('auto_sync', True))
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.log_message(f"加载配置失败: {str(e)}")
        
        return {}
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")
    
    def log_message(self, message: str):
        """记录日志消息"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def clear_log(self):
        """清除日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def update_status(self):
        """更新状态显示"""
        try:
            # 更新服务端状态
            if self.server:
                status = self.server.get_status()
                status_text = f"""运行状态: {'运行中' if status['running'] else '已停止'}
同步目录: {status['sync_directory']}
文件总数: {status['total_files']}
连接客户端: {status['connected_clients']}
客户端列表: {', '.join(status['clients'])}
"""
                self.server_status_text.config(state=tk.NORMAL)
                self.server_status_text.delete(1.0, tk.END)
                self.server_status_text.insert(1.0, status_text)
                self.server_status_text.config(state=tk.DISABLED)
            
            # 更新客户端状态
            if self.client:
                status = self.client.get_status()
                status_text = f"""客户端ID: {status['client_id']}
连接状态: {'已连接' if status['connected'] else '未连接'}
同步目录: {status['sync_directory']}
本地文件: {status['local_files']}
远程文件: {status['remote_files']}
待上传: {status['pending_uploads']}
待下载: {status['pending_downloads']}
同步进行中: {'是' if status['sync_in_progress'] else '否'}
自动同步: {'启用' if status['auto_sync'] else '禁用'}
"""
                self.client_status_text.config(state=tk.NORMAL)
                self.client_status_text.delete(1.0, tk.END)
                self.client_status_text.insert(1.0, status_text)
                self.client_status_text.config(state=tk.DISABLED)
        
        except Exception as e:
            pass  # 忽略状态更新错误
        
        # 每秒更新一次
        self.root.after(1000, self.update_status)
    
    def on_closing(self):
        """窗口关闭事件"""
        try:
            # 停止服务端和客户端
            if self.server:
                self.server.stop()
            if self.client:
                self.client.disconnect()
            
            # 保存配置
            self.save_config()
            
        except Exception as e:
            pass
        
        self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """GUI主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 启动GUI
    app = SyncGUI()
    app.run()

if __name__ == '__main__':
    main()
