"""
网络通信模块 - 实现隐蔽的TCP通信
包含连接管理、数据传输、流量伪装等功能
"""

import socket
import threading
import time
import logging
import secrets
from typing import Dict, Optional, Callable, Tuple
from queue import Queue, Empty
try:
    from .protocol import SecureProtocol, PacketType, PROTOCOL_CONFIG
except ImportError:
    from protocol import SecureProtocol, PacketType, PROTOCOL_CONFIG

logger = logging.getLogger(__name__)

class NetworkConnection:
    """网络连接封装"""
    
    def __init__(self, sock: socket.socket, address: Tuple[str, int]):
        self.socket = sock
        self.address = address
        self.protocol = SecureProtocol()
        self.last_heartbeat = time.time()
        self.authenticated = False
        self.client_id = ""
        self.send_queue = Queue()
        self.running = True
        
        # 启动发送线程
        self.send_thread = threading.Thread(target=self._send_worker, daemon=True)
        self.send_thread.start()
    
    def send_packet(self, packet_type: PacketType, payload: bytes = b''):
        """发送数据包"""
        packet = self.protocol.create_packet(packet_type, payload)
        self.send_queue.put(packet)
    
    def _send_worker(self):
        """发送工作线程"""
        while self.running:
            try:
                packet = self.send_queue.get(timeout=1.0)
                if packet is None:  # 停止信号
                    break
                
                # 添加流量伪装延迟
                time.sleep(secrets.randbelow(50) / 1000.0)  # 0-50ms随机延迟
                
                self._send_raw(packet)
                
            except Empty:
                continue
            except Exception as e:
                logger.error(f"发送数据失败: {e}")
                break
    
    def _send_raw(self, data: bytes):
        """发送原始数据"""
        try:
            # 发送数据长度
            length = len(data)
            self.socket.sendall(length.to_bytes(4, 'big'))
            
            # 发送数据
            self.socket.sendall(data)
            
        except Exception as e:
            logger.error(f"发送原始数据失败: {e}")
            raise
    
    def receive_packet(self) -> Optional[Tuple[PacketType, bytes]]:
        """接收数据包"""
        try:
            # 接收数据长度
            length_data = self._recv_exact(4)
            if not length_data:
                return None
            
            length = int.from_bytes(length_data, 'big')
            if length > SecureProtocol.MAX_PACKET_SIZE:
                raise ValueError(f"数据包过大: {length}")
            
            # 接收数据
            packet_data = self._recv_exact(length)
            if not packet_data:
                return None
            
            # 解析数据包
            result = self.protocol.parse_packet(packet_data)
            if result:
                packet_type, payload = result
                
                # 更新心跳时间
                if packet_type == PacketType.HEARTBEAT:
                    self.last_heartbeat = time.time()
                
                return result
            
        except Exception as e:
            logger.error(f"接收数据包失败: {e}")
            
        return None
    
    def _recv_exact(self, size: int) -> Optional[bytes]:
        """精确接收指定大小的数据"""
        data = b''
        while len(data) < size:
            try:
                chunk = self.socket.recv(size - len(data))
                if not chunk:
                    return None
                data += chunk
            except Exception:
                return None
        return data
    
    def close(self):
        """关闭连接"""
        self.running = False
        self.send_queue.put(None)  # 发送停止信号
        
        try:
            self.socket.close()
        except:
            pass

class SecureServer:
    """安全服务器"""
    
    def __init__(self, host: str = '0.0.0.0', port: int = None):
        self.host = host
        self.port = port or PROTOCOL_CONFIG['port']
        self.connections: Dict[str, NetworkConnection] = {}
        self.running = False
        self.server_socket: Optional[socket.socket] = None
        
        # 事件回调
        self.on_client_connected: Optional[Callable] = None
        self.on_client_disconnected: Optional[Callable] = None
        self.on_packet_received: Optional[Callable] = None
    
    def start(self):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 绑定到指定端口
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(PROTOCOL_CONFIG['max_connections'])
            
            self.running = True
            logger.info(f"服务器启动在 {self.host}:{self.port}")
            
            # 启动心跳检查线程
            heartbeat_thread = threading.Thread(target=self._heartbeat_worker, daemon=True)
            heartbeat_thread.start()
            
            # 主循环
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    logger.info(f"新客户端连接: {address}")
                    
                    # 创建连接对象
                    connection = NetworkConnection(client_socket, address)
                    
                    # 启动处理线程
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(connection,),
                        daemon=True
                    )
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        logger.error(f"接受连接失败: {e}")
        
        except Exception as e:
            logger.error(f"启动服务器失败: {e}")
            raise
    
    def stop(self):
        """停止服务器"""
        self.running = False
        
        # 关闭所有连接
        for connection in list(self.connections.values()):
            connection.close()
        self.connections.clear()
        
        # 关闭服务器套接字
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
    
    def _handle_client(self, connection: NetworkConnection):
        """处理客户端连接"""
        try:
            while self.running and connection.running:
                result = connection.receive_packet()
                if not result:
                    break
                
                packet_type, payload = result
                
                # 处理不同类型的数据包
                if packet_type == PacketType.HANDSHAKE_REQ:
                    self._handle_handshake(connection, payload)
                elif packet_type == PacketType.AUTH_REQ:
                    self._handle_auth(connection, payload)
                elif self.on_packet_received:
                    self.on_packet_received(connection, packet_type, payload)
        
        except Exception as e:
            logger.error(f"处理客户端失败: {e}")
        
        finally:
            self._disconnect_client(connection)
    
    def _handle_handshake(self, connection: NetworkConnection, payload: bytes):
        """处理握手请求"""
        try:
            if len(payload) < 4:
                return
            
            client_id_len = int.from_bytes(payload[:4], 'big')
            client_id = payload[4:4+client_id_len].decode('utf-8')
            
            connection.client_id = client_id
            
            # 生成会话密钥
            session_key = connection.protocol.generate_session_key()
            
            # 发送握手确认
            ack_packet = connection.protocol.create_handshake_ack(session_key)
            connection.send_queue.put(ack_packet)
            
            logger.info(f"握手完成: {client_id}")
            
        except Exception as e:
            logger.error(f"处理握手失败: {e}")
    
    def _handle_auth(self, connection: NetworkConnection, payload: bytes):
        """处理认证请求"""
        try:
            if len(payload) < 8:
                return
            
            username_len = int.from_bytes(payload[:4], 'big')
            password_len = int.from_bytes(payload[4:8], 'big')
            
            username = payload[8:8+username_len].decode('utf-8')
            password_hash = payload[8+username_len:8+username_len+password_len].decode('utf-8')
            
            # 简单认证（实际应用中应使用更安全的方式）
            if self._authenticate_user(username, password_hash):
                connection.authenticated = True
                self.connections[connection.client_id] = connection
                
                # 发送认证成功
                connection.send_packet(PacketType.AUTH_ACK, b'\x01')
                
                if self.on_client_connected:
                    self.on_client_connected(connection)
                
                logger.info(f"客户端认证成功: {username}")
            else:
                # 发送认证失败
                connection.send_packet(PacketType.AUTH_ACK, b'\x00')
                logger.warning(f"客户端认证失败: {username}")
        
        except Exception as e:
            logger.error(f"处理认证失败: {e}")
    
    def _authenticate_user(self, username: str, password_hash: str) -> bool:
        """用户认证（简化版本）"""
        # 这里应该实现真正的用户认证逻辑
        return username == "sync_user" and password_hash == "secure_hash_2025"
    
    def _disconnect_client(self, connection: NetworkConnection):
        """断开客户端连接"""
        if connection.client_id in self.connections:
            del self.connections[connection.client_id]
        
        connection.close()
        
        if self.on_client_disconnected:
            self.on_client_disconnected(connection)
        
        logger.info(f"客户端断开: {connection.address}")
    
    def _heartbeat_worker(self):
        """心跳检查工作线程"""
        while self.running:
            current_time = time.time()
            timeout = PROTOCOL_CONFIG['connection_timeout']
            
            # 检查超时连接
            for connection in list(self.connections.values()):
                if current_time - connection.last_heartbeat > timeout:
                    logger.warning(f"客户端心跳超时: {connection.client_id}")
                    self._disconnect_client(connection)
            
            time.sleep(PROTOCOL_CONFIG['heartbeat_interval'])

class SecureClient:
    """安全客户端"""
    
    def __init__(self, client_id: str):
        self.client_id = client_id
        self.connection: Optional[NetworkConnection] = None
        self.connected = False
        
        # 事件回调
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_packet_received: Optional[Callable] = None
    
    def connect(self, host: str, port: int = None) -> bool:
        """连接到服务器"""
        try:
            port = port or PROTOCOL_CONFIG['port']
            
            # 创建套接字
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10.0)  # 连接超时
            
            # 连接服务器
            sock.connect((host, port))
            
            # 创建连接对象
            self.connection = NetworkConnection(sock, (host, port))
            
            # 发送握手请求
            handshake_packet = self.connection.protocol.create_handshake_request(self.client_id)
            self.connection.send_queue.put(handshake_packet)
            
            # 等待握手响应
            result = self.connection.receive_packet()
            if result and result[0] == PacketType.HANDSHAKE_ACK:
                session_key = result[1]
                self.connection.protocol.session_key = session_key
                
                self.connected = True
                logger.info(f"连接成功: {host}:{port}")
                
                # 启动接收线程
                receive_thread = threading.Thread(target=self._receive_worker, daemon=True)
                receive_thread.start()
                
                # 启动心跳线程
                heartbeat_thread = threading.Thread(target=self._heartbeat_worker, daemon=True)
                heartbeat_thread.start()
                
                if self.on_connected:
                    self.on_connected()
                
                return True
        
        except Exception as e:
            logger.error(f"连接失败: {e}")
            
        return False
    
    def disconnect(self):
        """断开连接"""
        self.connected = False
        
        if self.connection:
            self.connection.close()
            self.connection = None
        
        if self.on_disconnected:
            self.on_disconnected()
    
    def send_packet(self, packet_type: PacketType, payload: bytes = b''):
        """发送数据包"""
        if self.connection and self.connected:
            self.connection.send_packet(packet_type, payload)
    
    def _receive_worker(self):
        """接收工作线程"""
        while self.connected and self.connection:
            try:
                result = self.connection.receive_packet()
                if not result:
                    break
                
                packet_type, payload = result
                
                if self.on_packet_received:
                    self.on_packet_received(packet_type, payload)
            
            except Exception as e:
                logger.error(f"接收数据失败: {e}")
                break
        
        self.disconnect()
    
    def _heartbeat_worker(self):
        """心跳工作线程"""
        while self.connected and self.connection:
            try:
                heartbeat_packet = self.connection.protocol.create_heartbeat()
                self.connection.send_queue.put(heartbeat_packet)
                
                time.sleep(PROTOCOL_CONFIG['heartbeat_interval'])
            
            except Exception as e:
                logger.error(f"发送心跳失败: {e}")
                break
