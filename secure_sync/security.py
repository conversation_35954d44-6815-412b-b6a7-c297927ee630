"""
安全特性模块 - 实现高级加密、身份验证和流量伪装
"""

import os
import hmac
import hashlib
import secrets
import time
import base64
from typing import Optional, Tuple, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class AdvancedCrypto:
    """高级加密系统"""
    
    def __init__(self, master_key: str = None):
        self.master_key = master_key or "SecureSync2025_MasterKey"
        self.session_keys: Dict[str, bytes] = {}
        
        # 生成RSA密钥对
        self.private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )
        self.public_key = self.private_key.public_key()
    
    def generate_session_key(self, client_id: str) -> bytes:
        """为客户端生成会话密钥"""
        # 使用PBKDF2生成强密钥
        salt = secrets.token_bytes(16)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key_material = f"{self.master_key}:{client_id}:{time.time()}".encode()
        session_key = kdf.derive(key_material)
        
        self.session_keys[client_id] = session_key
        return session_key
    
    def encrypt_data(self, data: bytes, client_id: str) -> bytes:
        """使用会话密钥加密数据"""
        if client_id not in self.session_keys:
            raise ValueError(f"No session key for client: {client_id}")
        
        # 使用Fernet对称加密
        fernet = Fernet(base64.urlsafe_b64encode(self.session_keys[client_id]))
        return fernet.encrypt(data)
    
    def decrypt_data(self, encrypted_data: bytes, client_id: str) -> bytes:
        """使用会话密钥解密数据"""
        if client_id not in self.session_keys:
            raise ValueError(f"No session key for client: {client_id}")
        
        fernet = Fernet(base64.urlsafe_b64encode(self.session_keys[client_id]))
        return fernet.decrypt(encrypted_data)
    
    def rsa_encrypt(self, data: bytes) -> bytes:
        """RSA公钥加密"""
        return self.public_key.encrypt(
            data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
    
    def rsa_decrypt(self, encrypted_data: bytes) -> bytes:
        """RSA私钥解密"""
        return self.private_key.decrypt(
            encrypted_data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
    
    def get_public_key_pem(self) -> bytes:
        """获取公钥PEM格式"""
        return self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )

class AuthenticationManager:
    """身份验证管理器"""
    
    def __init__(self):
        self.users: Dict[str, Dict[str, Any]] = {}
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.failed_attempts: Dict[str, int] = {}
        self.max_failed_attempts = 5
        self.lockout_duration = 300  # 5分钟
    
    def add_user(self, username: str, password: str, permissions: list = None):
        """添加用户"""
        salt = secrets.token_hex(16)
        password_hash = self._hash_password(password, salt)
        
        self.users[username] = {
            'password_hash': password_hash,
            'salt': salt,
            'permissions': permissions or ['read', 'write'],
            'created_at': time.time(),
            'last_login': None
        }
    
    def authenticate(self, username: str, password: str, client_ip: str) -> Optional[str]:
        """用户认证"""
        # 检查是否被锁定
        if self._is_locked_out(client_ip):
            return None
        
        if username not in self.users:
            self._record_failed_attempt(client_ip)
            return None
        
        user = self.users[username]
        password_hash = self._hash_password(password, user['salt'])
        
        if password_hash == user['password_hash']:
            # 认证成功
            session_token = self._generate_session_token()
            self.active_sessions[session_token] = {
                'username': username,
                'client_ip': client_ip,
                'login_time': time.time(),
                'last_activity': time.time(),
                'permissions': user['permissions']
            }
            
            user['last_login'] = time.time()
            self._clear_failed_attempts(client_ip)
            
            return session_token
        else:
            # 认证失败
            self._record_failed_attempt(client_ip)
            return None
    
    def validate_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """验证会话"""
        if session_token not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_token]
        
        # 检查会话是否过期（24小时）
        if time.time() - session['last_activity'] > 86400:
            del self.active_sessions[session_token]
            return None
        
        # 更新最后活动时间
        session['last_activity'] = time.time()
        return session
    
    def logout(self, session_token: str):
        """注销会话"""
        if session_token in self.active_sessions:
            del self.active_sessions[session_token]
    
    def _hash_password(self, password: str, salt: str) -> str:
        """密码哈希"""
        return hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()
    
    def _generate_session_token(self) -> str:
        """生成会话令牌"""
        return secrets.token_urlsafe(32)
    
    def _is_locked_out(self, client_ip: str) -> bool:
        """检查IP是否被锁定"""
        if client_ip not in self.failed_attempts:
            return False
        
        attempts, last_attempt = self.failed_attempts[client_ip]
        
        if attempts >= self.max_failed_attempts:
            if time.time() - last_attempt < self.lockout_duration:
                return True
            else:
                # 锁定期已过，清除记录
                del self.failed_attempts[client_ip]
        
        return False
    
    def _record_failed_attempt(self, client_ip: str):
        """记录失败尝试"""
        if client_ip in self.failed_attempts:
            attempts, _ = self.failed_attempts[client_ip]
            self.failed_attempts[client_ip] = (attempts + 1, time.time())
        else:
            self.failed_attempts[client_ip] = (1, time.time())
    
    def _clear_failed_attempts(self, client_ip: str):
        """清除失败尝试记录"""
        if client_ip in self.failed_attempts:
            del self.failed_attempts[client_ip]

class TrafficObfuscator:
    """流量混淆器"""
    
    def __init__(self):
        self.fake_http_headers = [
            b"GET /api/v1/status HTTP/1.1\r\n",
            b"POST /upload HTTP/1.1\r\n",
            b"PUT /data HTTP/1.1\r\n",
            b"DELETE /cache HTTP/1.1\r\n"
        ]
        
        self.fake_dns_queries = [
            b"google.com",
            b"microsoft.com",
            b"cloudflare.com",
            b"amazon.com"
        ]
    
    def obfuscate_packet(self, data: bytes) -> bytes:
        """混淆数据包"""
        # 添加假HTTP头
        fake_header = secrets.choice(self.fake_http_headers)
        
        # 添加随机填充
        padding_size = secrets.randbelow(64) + 16
        padding = secrets.token_bytes(padding_size)
        
        # 构造混淆包
        obfuscated = fake_header + b"Content-Length: " + str(len(data)).encode() + b"\r\n\r\n"
        obfuscated += data + padding
        
        return obfuscated
    
    def deobfuscate_packet(self, obfuscated_data: bytes) -> Optional[bytes]:
        """去混淆数据包"""
        try:
            # 查找HTTP头结束位置
            header_end = obfuscated_data.find(b"\r\n\r\n")
            if header_end == -1:
                return None
            
            # 提取Content-Length
            header_part = obfuscated_data[:header_end]
            content_length_start = header_part.find(b"Content-Length: ")
            if content_length_start == -1:
                return None
            
            content_length_start += len(b"Content-Length: ")
            content_length_end = header_part.find(b"\r\n", content_length_start)
            if content_length_end == -1:
                content_length_end = header_end
            
            content_length = int(header_part[content_length_start:content_length_end])
            
            # 提取实际数据
            data_start = header_end + 4
            actual_data = obfuscated_data[data_start:data_start + content_length]
            
            return actual_data
        
        except Exception:
            return None
    
    def generate_fake_traffic(self) -> bytes:
        """生成假流量"""
        fake_query = secrets.choice(self.fake_dns_queries)
        fake_response = secrets.token_bytes(secrets.randbelow(512) + 64)
        
        return fake_query + b"\x00" + fake_response

class SecurityManager:
    """安全管理器 - 整合所有安全功能"""
    
    def __init__(self, master_key: str = None):
        self.crypto = AdvancedCrypto(master_key)
        self.auth = AuthenticationManager()
        self.obfuscator = TrafficObfuscator()
        
        # 添加默认用户
        self.auth.add_user("sync_user", "SecurePassword2025!", ['read', 'write', 'admin'])
        self.auth.add_user("readonly_user", "ReadOnlyPass2025!", ['read'])
    
    def secure_packet(self, data: bytes, client_id: str) -> bytes:
        """安全化数据包"""
        # 1. 加密数据
        encrypted_data = self.crypto.encrypt_data(data, client_id)
        
        # 2. 添加HMAC
        hmac_key = self.crypto.session_keys.get(client_id, b'default_key')
        signature = hmac.new(hmac_key, encrypted_data, hashlib.sha256).digest()
        
        # 3. 组合数据
        secure_data = signature + encrypted_data
        
        # 4. 混淆流量
        obfuscated_data = self.obfuscator.obfuscate_packet(secure_data)
        
        return obfuscated_data
    
    def unsecure_packet(self, obfuscated_data: bytes, client_id: str) -> Optional[bytes]:
        """解除数据包安全化"""
        try:
            # 1. 去混淆
            secure_data = self.obfuscator.deobfuscate_packet(obfuscated_data)
            if not secure_data:
                return None
            
            # 2. 分离签名和数据
            if len(secure_data) < 32:  # SHA256签名长度
                return None
            
            signature = secure_data[:32]
            encrypted_data = secure_data[32:]
            
            # 3. 验证HMAC
            hmac_key = self.crypto.session_keys.get(client_id, b'default_key')
            expected_signature = hmac.new(hmac_key, encrypted_data, hashlib.sha256).digest()
            
            if not hmac.compare_digest(signature, expected_signature):
                return None
            
            # 4. 解密数据
            decrypted_data = self.crypto.decrypt_data(encrypted_data, client_id)
            
            return decrypted_data
        
        except Exception:
            return None
    
    def create_secure_session(self, username: str, password: str, client_ip: str) -> Optional[Tuple[str, bytes]]:
        """创建安全会话"""
        # 身份验证
        session_token = self.auth.authenticate(username, password, client_ip)
        if not session_token:
            return None
        
        # 生成会话密钥
        session_key = self.crypto.generate_session_key(session_token)
        
        return session_token, session_key
    
    def validate_secure_session(self, session_token: str) -> bool:
        """验证安全会话"""
        session = self.auth.validate_session(session_token)
        return session is not None
    
    def get_session_permissions(self, session_token: str) -> list:
        """获取会话权限"""
        session = self.auth.validate_session(session_token)
        if session:
            return session.get('permissions', [])
        return []
