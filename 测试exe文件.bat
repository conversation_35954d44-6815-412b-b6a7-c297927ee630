@echo off
chcp 65001 >nul
title 测试安全同步工具EXE文件

echo ============================================================
echo                  测试安全同步工具EXE文件
echo ============================================================
echo.

echo 检查文件是否存在...
if exist "dist_secure_sync\SecureSync.exe" (
    echo ✓ SecureSync.exe 文件存在
) else (
    echo ✗ SecureSync.exe 文件不存在
    pause
    exit
)

echo.
echo 检查文件大小...
for %%A in ("dist_secure_sync\SecureSync.exe") do (
    echo ✓ 文件大小: %%~zA 字节 (约 14.4MB)
)

echo.
echo 测试命令行参数...
echo 运行: SecureSync.exe --help
echo.
dist_secure_sync\SecureSync.exe --help

echo.
echo ============================================================
echo 测试完成！
echo.
echo 如果上面没有显示帮助信息，说明exe文件可能有问题。
echo 如果显示了帮助信息，说明exe文件正常。
echo.
echo 现在可以尝试运行图形界面:
echo dist_secure_sync\SecureSync.exe gui
echo ============================================================
echo.
pause
