#!/usr/bin/env python3
"""
安全文件同步工具 - 系统诊断工具
用于诊断跨系统兼容性问题
"""

import os
import sys
import socket
import platform
import threading
import time
import traceback
from pathlib import Path

def print_system_info():
    """打印系统信息"""
    print("=" * 60)
    print("系统信息诊断")
    print("=" * 60)
    
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"系统版本: {platform.version()}")
    print(f"处理器架构: {platform.machine()}")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"用户名: {os.getenv('USERNAME', 'Unknown')}")
    print(f"计算机名: {os.getenv('COMPUTERNAME', 'Unknown')}")
    print()

def test_network_binding():
    """测试网络绑定"""
    print("=" * 60)
    print("网络绑定测试")
    print("=" * 60)
    
    test_ports = [23847, 23848, 23849]
    
    for port in test_ports:
        try:
            # 测试TCP绑定
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind(('0.0.0.0', port))
            sock.listen(1)
            print(f"✓ 端口 {port} 绑定成功")
            sock.close()
        except Exception as e:
            print(f"✗ 端口 {port} 绑定失败: {e}")
    
    print()

def test_file_operations():
    """测试文件操作"""
    print("=" * 60)
    print("文件操作测试")
    print("=" * 60)
    
    test_dir = Path("test_sync_dir")
    
    try:
        # 创建测试目录
        test_dir.mkdir(exist_ok=True)
        print(f"✓ 创建目录成功: {test_dir.absolute()}")
        
        # 创建测试文件
        test_file = test_dir / "test.txt"
        test_file.write_text("测试文件内容", encoding='utf-8')
        print(f"✓ 创建文件成功: {test_file}")
        
        # 读取文件
        content = test_file.read_text(encoding='utf-8')
        print(f"✓ 读取文件成功: {len(content)} 字符")
        
        # 删除测试文件
        test_file.unlink()
        test_dir.rmdir()
        print("✓ 清理测试文件成功")
        
    except Exception as e:
        print(f"✗ 文件操作失败: {e}")
        traceback.print_exc()
    
    print()

def test_threading():
    """测试线程功能"""
    print("=" * 60)
    print("线程功能测试")
    print("=" * 60)
    
    results = []
    
    def worker_thread(thread_id):
        try:
            time.sleep(0.1)
            results.append(f"线程{thread_id}完成")
        except Exception as e:
            results.append(f"线程{thread_id}失败: {e}")
    
    try:
        threads = []
        for i in range(3):
            thread = threading.Thread(target=worker_thread, args=(i,), daemon=True)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=5)
        
        print(f"✓ 线程测试完成，结果: {results}")
        
    except Exception as e:
        print(f"✗ 线程测试失败: {e}")
        traceback.print_exc()
    
    print()

def test_imports():
    """测试关键模块导入"""
    print("=" * 60)
    print("模块导入测试")
    print("=" * 60)
    
    modules_to_test = [
        'socket',
        'threading',
        'time',
        'os',
        'sys',
        'pathlib',
        'json',
        'hashlib',
        'secrets',
        'struct',
        'zlib',
        'tkinter',
        'watchdog',
        'cryptography'
    ]
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {module_name}")
        except ImportError as e:
            print(f"✗ {module_name}: {e}")
        except Exception as e:
            print(f"? {module_name}: {e}")
    
    print()

def test_minimal_server():
    """测试最小化服务器"""
    print("=" * 60)
    print("最小化服务器测试")
    print("=" * 60)
    
    def simple_server():
        try:
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind(('127.0.0.1', 23847))
            server_socket.listen(1)
            server_socket.settimeout(2.0)
            
            print("✓ 简单服务器启动成功，监听 127.0.0.1:23847")
            
            # 等待连接（超时2秒）
            try:
                conn, addr = server_socket.accept()
                print(f"✓ 接收到连接: {addr}")
                conn.close()
            except socket.timeout:
                print("- 无连接请求（正常）")
            
            server_socket.close()
            print("✓ 服务器正常关闭")
            
        except Exception as e:
            print(f"✗ 简单服务器测试失败: {e}")
            traceback.print_exc()
    
    # 在线程中运行服务器
    server_thread = threading.Thread(target=simple_server, daemon=True)
    server_thread.start()
    server_thread.join(timeout=5)
    
    print()

def test_permissions():
    """测试权限"""
    print("=" * 60)
    print("权限测试")
    print("=" * 60)
    
    try:
        # 测试当前目录写权限
        test_file = Path("permission_test.tmp")
        test_file.write_text("test")
        test_file.unlink()
        print("✓ 当前目录写权限正常")
    except Exception as e:
        print(f"✗ 当前目录写权限失败: {e}")
    
    try:
        # 测试临时目录权限
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', delete=True) as f:
            f.write("test")
        print("✓ 临时目录权限正常")
    except Exception as e:
        print(f"✗ 临时目录权限失败: {e}")
    
    print()

def main():
    """主函数"""
    print("安全文件同步工具 - 系统诊断")
    print("用于诊断跨系统兼容性问题")
    print()
    
    try:
        print_system_info()
        test_permissions()
        test_imports()
        test_file_operations()
        test_threading()
        test_network_binding()
        test_minimal_server()
        
        print("=" * 60)
        print("诊断完成")
        print("=" * 60)
        print()
        print("如果发现问题，请将诊断结果发送给开发者")
        print("特别注意标记为 ✗ 的失败项目")
        
    except Exception as e:
        print(f"诊断过程中发生异常: {e}")
        traceback.print_exc()
    
    input("\n按回车键退出...")

if __name__ == '__main__':
    main()
