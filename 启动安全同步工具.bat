@echo off
chcp 65001 >nul
title 安全文件同步工具

echo ============================================================
echo                    安全文件同步工具 v1.0.0
echo                使用私有协议的隐蔽文件同步软件
echo ============================================================
echo.

:menu
echo 请选择运行模式:
echo.
echo [1] 图形界面模式 (推荐)
echo [2] 服务端模式
echo [3] 客户端模式
echo [4] 查看帮助
echo [0] 退出
echo.
set /p choice=请输入选择 (0-4): 

if "%choice%"=="1" goto gui_mode
if "%choice%"=="2" goto server_mode
if "%choice%"=="3" goto client_mode
if "%choice%"=="4" goto help_mode
if "%choice%"=="0" goto exit
echo 无效选择，请重新输入
echo.
goto menu

:gui_mode
echo.
echo 启动图形界面模式...
echo 注意: 如果是首次运行，Windows可能会弹出防火墙提示，请选择"允许访问"
echo.
dist_secure_sync\SecureSync.exe gui
goto end

:server_mode
echo.
echo 启动服务端模式
echo.
set /p server_dir=请输入同步目录路径: 
set /p server_host=请输入监听地址 (默认 0.0.0.0): 
set /p server_port=请输入监听端口 (默认 23847): 

if "%server_host%"=="" set server_host=0.0.0.0
if "%server_port%"=="" set server_port=23847

echo.
echo 启动服务端...
echo 同步目录: %server_dir%
echo 监听地址: %server_host%:%server_port%
echo.
echo 按 Ctrl+C 停止服务端
echo.
dist_secure_sync\SecureSync.exe server -d "%server_dir%" --host %server_host% --port %server_port%
goto end

:client_mode
echo.
echo 启动客户端模式
echo.
set /p client_dir=请输入本地同步目录路径: 
set /p server_ip=请输入服务器IP地址: 
set /p server_port=请输入服务器端口 (默认 23847): 
set /p client_id=请输入客户端ID (默认 client_001): 

if "%server_port%"=="" set server_port=23847
if "%client_id%"=="" set client_id=client_001

echo.
echo 启动客户端...
echo 本地目录: %client_dir%
echo 服务器: %server_ip%:%server_port%
echo 客户端ID: %client_id%
echo.
echo 按 Ctrl+C 断开连接
echo.
dist_secure_sync\SecureSync.exe client -d "%client_dir%" -s %server_ip% --port %server_port% --client-id %client_id%
goto end

:help_mode
echo.
echo ============================================================
echo                        使用帮助
echo ============================================================
echo.
echo 1. 图形界面模式 (推荐新手使用)
echo    - 提供完整的图形化配置界面
echo    - 可以同时运行服务端和客户端
echo    - 实时显示连接状态和同步进度
echo.
echo 2. 服务端模式
echo    - 在一台电脑上运行，作为文件同步的中心
echo    - 需要指定同步目录路径
echo    - 默认监听所有网络接口 (0.0.0.0)
echo    - 默认端口 23847
echo.
echo 3. 客户端模式  
echo    - 连接到服务端进行文件同步
echo    - 需要指定本地同步目录和服务器地址
echo    - 支持多个客户端同时连接一个服务端
echo.
echo 安全特性:
echo - 使用非常用端口 (23847)，避免被扫描
echo - 自定义加密协议，数据传输安全
echo - 流量伪装技术，难以被检测
echo - 实时文件监控和增量同步
echo.
echo 网络要求:
echo - 确保防火墙允许指定端口通信
echo - 服务端和客户端需要在同一局域网
echo - 或者通过VPN等方式建立网络连接
echo.
echo ============================================================
echo.
pause
goto menu

:exit
echo.
echo 感谢使用安全文件同步工具！
echo.
goto end

:end
pause
