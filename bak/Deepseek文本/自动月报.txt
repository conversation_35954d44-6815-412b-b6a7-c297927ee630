编写一个报告自动生成程序，要求有如下功能：
1、通过弹窗的方式选择一个时间区间，同时给出两个快捷选择按钮“周报”和“月报”，如选择“周报”则将时间起止设定为当前日期向前推7天（共7天），如选择“月报”，则将起始时间设定为上个月的后一天（如今天是2025年5月17日，则开始时间是2025年4月18日），结束日期为当天，默认选择周报。同时将开始日期里面的“月”定义为month_s，“日”定义为“day_s”，开结束日期里面的“月”定义为month_e，“日”定义为“day_e”。
2、打开当前目录下“越南湿租报告”文件夹下的“Vietnam_Report.xlsx”文件。这个excel文件是飞行数据，其中每行代表一段航班，每列代表这段航班中的一些关键参数。读取E列数据，这是航班日期信息（格式为“yyyy-mm-dd”）,如果单元格内的日期处于前面选择日期起止之间（含起止日期），则此行的数据有效，记录下，后面参与计算时需使用。
3、后续所有计算均是限定在前面选择的时间区间内进行。
4、定义如下参数:
字符型：monthly_weekly
整型：flight_num、b_652g_num、b_656E_num、wqar_num
浮点型：gw_to_avg、wind_tail_to_avg、wind_cross_to_avg、dist_to_avg、vr_to_avg、pitch_to_avg、pitch_r_to_max、pitch_r_to_avg、head_to_avg、roll_to_avg、gw_ld_avg、fuel_ld_avg、wind_tail_ld、wind_cross_ld、alt_stab_avg、alt_thr_avg、alt_s_flare_avg、alt_flare_avg、dist_td_avg、dist_40_avg、vapp_dev_avg、pitch_ld_avg、vrtg_avg、roll_ld_avg、head_ld_avg、thr_avg、rev_avg、rev_dura_avg、head_to_max、dist_td_max、dist_40_max、head_ld_max、roll_ld_max、roll_to_max
5、依次计算参数，具体方法如下：
monthly_weekly，若第2步中选择的时间区间<15天，则参数monthly_weekly="周"，否则monthly_weekly="月"；
b_652g_num，B-652G飞机总航段数；
b_656E_num，B-656E飞机总航段数；
flight_num，b_652g_num+b_656E_num；
wqar_num，b_652g_num+b_656E_num；
下面是与起飞相关参数（只统计起飞机场是“VVCS”的航班）
gw_to_avg，“起飞重量”平均值
wind_tail_to_avg，“起飞平均顺风”平均值
wind_cross_to_avg，“起飞最大侧风”平均值
dist_to_avg，“起飞滑跑距离”平均值
vr_to_avg，“抬轮与VR时差”平均值
pitch_to_avg，“离地姿态”平均值
pitch_r_to_max，“抬轮最大速率”平均值
pitch_r_to_avg，“抬轮平均速率”平均值
head_to_avg，“起飞滑跑航向偏离”平均值
roll_to_avg，“起飞最大坡度”平均值
head_to_max，“起飞滑跑航向偏离”最大值
roll_to_max，“起飞最大坡度”最大值
下面是与着陆相关参数（只统计着陆机场是“VVCS”的航班）
gw_ld_avg，“着陆重量”平均值
fuel_ld_avg，“着陆剩余燃油”平均值
wind_tail_ld，“着陆平均顺风”平均值
wind_cross_ld，“着陆最大侧风”平均值
alt_stab_avg，“稳定进近高度”平均值
alt_thr_avg，“进跑道高度”平均值
alt_s_flare_avg，“拉开始高度”平均值
alt_flare_avg，“接近拉平高度”平均值
dist_td_avg，“接地点距离”平均值
dist_40_avg，“入口至减速到40节距离”平均值
vapp_dev_avg，“进近速度偏差”平均值
pitch_ld_avg，“着陆姿态”平均值
vrtg_avg，“着陆载荷”平均值
roll_ld_avg，“着陆坡度”平均值
head_ld_avg，“滑跑航向偏离”平均值
thr_avg，“收光油门时机”平均值
rev_avg，“开反推时间”平均值
rev_dura_avg，“反推使用时长”平均值
dist_td_max，“接地点距离”最大值
dist_40_max，“入口至减速到40节距离”最大值
head_ld_max，“滑跑航向偏离”最大值
roll_ld_max“着陆坡度”最大值
6、打开“Template”文件夹下的“C909越南运行QAR报告(模板).docx”文件。依次查找上面的参数名，并将其替换为具体数值，同时去除掉word文件中各参数名左右的“{”和“}”符号。
7、绘制图表，以excel数据为基础绘制时间序列曲线图，1）与起飞有关的："起飞滑跑距离"，“起飞滑跑距离”，“起飞平均顺风”，“起飞滑跑航向偏离”，“起飞坡度”；2）与着陆相关的：“接地点距离”，“着陆减速至40节距离”，“滑跑航向偏离”，“着陆坡度”。3）个别名字可能与excel里不是一一对应，用你的理解将认为是同一个参数的等同到一起。4）对于与起飞有关的图表，绘制时只选择起飞机场是“VVCS”的数据；5）对于与着陆有关的图表，绘制时只选择着陆机场是“VVCS”的数据。6）对于相同日期有多段数据按照时间先后在横轴上做均分处理，再绘点，将各点连接起来，形成曲线。
8、各个图表具体要求如下：
"起飞滑跑距离",最大值1800，最小值400，绿色平均值线，在1820的位置增画一条红线，定义为“跑道长度”，替换到word文件中{pic_dist_to}位置；
“起飞平均顺风”，最大值8，最小值-12；在0位置画一条黑线，上面标注为“顺风”，下面标注为“顶风”；
“起飞滑跑航向偏离”，最大值5，最小值0，绿色平均值线，在5的位置画一条红线，定义为“三级事件标准”，在3的位置画一条橙色线，定义为“二级事件标准”，替换到word文件中{pic_head_to}位置；
“起飞坡度”，最大值6，最小值0，绿色平均值线，在6的位置画一条红线，定义为“三级事件标准”，在5的位置画一条橙色线，定义为“二级事件标准”，替换到word文件中{pic_roll_to}位置；
“接地点距离”,最大值600，最小值0，绿色平均值线，在600的位置画一条红线，定义为“决策点”，替换到word文件中{pic_dist_ld}位置；
“着陆减速至40节距离”，最大值1800，绿色平均值线，最小值1200，在1820的位置画一条红线，定义为“跑道长度”，在1700的位置画一条橙色线，定义为“预警线”，替换到word文件中{pic_dist_40}位置；
“滑跑航向偏离”，最大值6，最小值0，绿色平均值线，在6的位置画一条红线，定义为“三级事件标准”，在5的位置画一条橙色线，定义为“二级事件标准”，替换到word文件中{pic_head_ld}位置；
“着陆坡度”,最大值6，最小值0，绿色平均值线，在5的位置画一条红线，定义为“三级事件标准”，在4的位置画一条橙色线，定义为“二级事件标准”，替换到word文件中{pic_roll_ld}位置；
前面绘制所有线条都统一为实线，宽度2.5磅（若条线与横轴线重叠，则线条置顶，覆盖显示），图表横轴和纵轴部分文字的字体使用“微软雅黑”（若没有此字体则自动使用其他可用中文字体），加粗，12号。
9、修改完成后的word文件另存到当前路径下，命名为"报告.docx"
10、上传的两个excel和word文件时模板，请参考。