import os
import tkinter as tk
from tkinter import messagebox, ttk
from tkcalendar import Calendar
from datetime import datetime, timedelta
import pandas as pd
from docx import Document
from docx.shared import Inches
import matplotlib.pyplot as plt
import tempfile
import numpy as np
import re
from docx.shared import Pt
from docx.oxml.ns import qn
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class DateRangeDialog(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent)
        self.title("选择时间区间")
        self.geometry("600x400")

        self.add_quick_buttons()
        self.add_calendars()

        ttk.Button(self, text="确定", command=self.on_confirm).pack(pady=10)
        self.result = None

    def add_quick_buttons(self):
        frame = ttk.Frame(self)
        frame.pack(pady=10)

        ttk.Button(frame, text="周报", command=self.set_weekly_range).pack(side=tk.LEFT, padx=5)
        ttk.Button(frame, text="月报", command=self.set_monthly_range).pack(side=tk.LEFT, padx=5)

    def add_calendars(self):
        frame = ttk.Frame(self)
        frame.pack(pady=10)

        ttk.Label(frame, text="开始日期:").grid(row=0, column=0, padx=5)
        self.start_cal = Calendar(frame, selectmode='day', date_pattern='yyyy-mm-dd')
        self.start_cal.grid(row=1, column=0, padx=5)

        ttk.Label(frame, text="结束日期:").grid(row=0, column=1, padx=5)
        self.end_cal = Calendar(frame, selectmode='day', date_pattern='yyyy-mm-dd')
        self.end_cal.grid(row=1, column=1, padx=5)

        # 默认设置为周报范围
        self.set_weekly_range()

    def set_weekly_range(self):
        """设置周报范围：6天前到今天（共7天）"""
        today = datetime.now().date()
        start_date = today - timedelta(days=6)  # 今天-6天=7天范围
        self.start_cal.selection_set(start_date)
        self.end_cal.selection_set(today)

    def set_monthly_range(self):
        """设置月报范围：上月同日+1天到今天"""
        today = datetime.now().date()

        # 计算上月同日+1天
        if today.month == 1:  # 如果是1月，上月是去年12月
            last_month_day = today.replace(year=today.year - 1, month=12, day=today.day)
        else:
            last_month_day = today.replace(month=today.month - 1, day=today.day)

        start_date = last_month_day + timedelta(days=1)

        # 如果计算出的开始日期大于今天（如1月31日对应2月1日不存在的情况）
        if start_date > today:
            start_date = today.replace(day=1)  # 回退到本月第一天

        self.start_cal.selection_set(start_date)
        self.end_cal.selection_set(today)

    def on_confirm(self):
        start_date = self.start_cal.get_date()
        end_date = self.end_cal.get_date()

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

            if start_date > end_date:
                messagebox.showerror("错误", "开始日期不能晚于结束日期")
                return

            self.result = (start_date, end_date)
            self.destroy()
        except ValueError:
            messagebox.showerror("错误", "日期格式无效")

class ReportGenerator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()

        self.init_default_values()
        self.show_date_range_dialog()

        if not hasattr(self, 'start_date') or not hasattr(self, 'end_date'):
            return

        try:
            self.read_excel_data()
            self.calculate_parameters()
            self.generate_word_report()  # 直接调用，不显示完成提示
        except Exception as e:
            messagebox.showerror("错误", f"程序运行失败: {str(e)}")
        finally:
            self.root.destroy()  # 确保窗口关闭

    def init_default_values(self):
        """初始化所有参数默认值"""
        # 基本参数
        self.monthly_weekly = "周"
        self.month_s = self.day_s = self.month_e = self.day_e = 0
        self.flight_num = self.b_652g_num = self.b_656E_num = self.wqar_num = 0

        # 起飞参数默认值
        self.gw_to_avg = self.wind_tail_to_avg = self.wind_cross_to_avg = 0.0
        self.dist_to_avg = self.vr_to_avg = self.pitch_to_avg = 0.0
        self.pitch_r_to_max = self.pitch_r_to_avg = self.head_to_avg = 0.0
        self.roll_to_avg = self.head_to_max = 0.0
        self.roll_to_max = self.dist_to_max = 0.0

        # 着陆参数默认值
        self.gw_ld_avg = self.fuel_ld_avg = self.wind_tail_ld = 0.0
        self.wind_cross_ld = self.alt_stab_avg = self.alt_thr_avg = 0.0
        self.alt_s_flare_avg = self.alt_flare_avg = self.dist_td_avg = 0.0
        self.dist_40_avg = self.vapp_dev_avg = self.pitch_ld_avg = 0.0
        self.vrtg_avg = self.roll_ld_avg = self.head_ld_avg = 0.0
        self.thr_avg = self.rev_avg = self.rev_dura_avg = 0.0
        self.dist_td_max = self.dist_40_max = 0
        self.head_ld_max = self.roll_ld_max = 0.0

    def show_date_range_dialog(self):
        dialog = DateRangeDialog(self.root)
        self.root.wait_window(dialog)

        if dialog.result:
            self.start_date, self.end_date = dialog.result
            delta = self.end_date - self.start_date
            self.monthly_weekly = "月" if delta.days >= 15 else "周"

            self.month_s = self.start_date.month
            self.day_s = self.start_date.day
            self.month_e = self.end_date.month
            self.day_e = self.end_date.day
        else:
            messagebox.showwarning("警告", "未选择日期范围，程序将退出")
            raise SystemExit

    def read_excel_data(self):
        excel_path = os.path.join("越南湿租报告", "Vietnam_Report.xlsx")
        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

        self.df = pd.read_excel(excel_path)

        # 验证必需的列是否存在
        required_columns = [
            '飞机号', '起飞机场', '着陆机场', '起飞日期', '起飞重量',
            '滑跑航向偏离', '着陆坡度'
        ]

        missing_columns = [col for col in required_columns if col not in self.df.columns]
        if missing_columns:
            raise ValueError(f"Excel文件中缺少必要的列: {missing_columns}")

        self.df['起飞日期'] = pd.to_datetime(self.df['起飞日期']).dt.date
        self.df = self.df[(self.df['起飞日期'] >= self.start_date) &
                          (self.df['起飞日期'] <= self.end_date)]

        if self.df.empty:
            raise ValueError("选定日期范围内没有航班数据")

    def safe_mean(self, series):
        """安全计算平均值，处理空值和NaN"""
        if series.empty:
            return 0.0
        try:
            return float(series.mean(skipna=True))
        except:
            return 0.0

    def safe_max(self, series):
        """安全计算最大值，处理空值和NaN"""
        if series.empty:
            return 0.0
        try:
            return float(series.max(skipna=True))
        except:
            return 0.0

    def calculate_parameters(self):
        """计算所有参数并处理可能的NaN值"""
        # 计算基本参数
        self.b_652g_num = len(self.df[self.df['飞机号'] == 'B-652G'])
        self.b_656E_num = len(self.df[self.df['飞机号'] == 'B-656E'])
        self.flight_num = self.b_652g_num + self.b_656E_num
        self.wqar_num = self.flight_num

        # 起飞相关参数
        to_df = self.df[self.df['起飞机场'] == 'VVCS']
        if not to_df.empty:
            self.gw_to_avg = self.safe_mean(to_df['起飞重量'])
            self.wind_tail_to_avg = self.safe_mean(to_df['起飞平均顺风'])
            self.wind_cross_to_avg = self.safe_mean(to_df['起飞最大侧风'])
            self.dist_to_avg = self.safe_mean(to_df['起飞滑跑距离'])
            self.vr_to_avg = self.safe_mean(to_df['抬轮与VR时差'])
            self.pitch_to_avg = self.safe_mean(to_df['离地姿态'])
            self.pitch_r_to_max = self.safe_mean(to_df['抬轮最大速率'])
            self.pitch_r_to_avg = self.safe_mean(to_df['抬轮平均速率'])
            self.head_to_avg = self.safe_mean(to_df['起飞滑跑航向偏离'])
            self.roll_to_avg = self.safe_mean(to_df['起飞最大坡度'])
            self.head_to_max = self.safe_max(to_df['起飞滑跑航向偏离'])
            self.roll_to_max = self.safe_max(to_df['起飞最大坡度'])
            self.dist_to_max = self.safe_max(to_df['起飞滑跑距离'])

        # 着陆相关参数
        ld_df = self.df[self.df['着陆机场'] == 'VVCS']
        if not ld_df.empty:
            self.gw_ld_avg = self.safe_mean(ld_df['着陆重量'])
            self.fuel_ld_avg = self.safe_mean(ld_df['着陆剩余燃油'])
            self.wind_tail_ld = self.safe_mean(ld_df['着陆平均顺风'])
            self.wind_cross_ld = self.safe_mean(ld_df['着陆最大侧风'])
            self.alt_stab_avg = self.safe_mean(ld_df['稳定进近高度'])
            self.alt_thr_avg = self.safe_mean(ld_df['进跑道高度'])
            self.alt_s_flare_avg = self.safe_mean(ld_df['拉开始高度'])
            self.alt_flare_avg = self.safe_mean(ld_df['接近拉平高度'])
            self.dist_td_avg = self.safe_mean(ld_df['接地点距离'])
            self.dist_40_avg = self.safe_mean(ld_df['入口至减速到40节距离'])
            self.vapp_dev_avg = self.safe_mean(ld_df['进近速度偏差'])
            self.pitch_ld_avg = self.safe_mean(ld_df['着陆姿态'])
            self.vrtg_avg = self.safe_mean(ld_df['着陆载荷'])
            self.roll_ld_avg = self.safe_mean(ld_df['着陆坡度'])
            self.head_ld_avg = self.safe_mean(ld_df['滑跑航向偏离'])
            self.thr_avg = self.safe_mean(ld_df['收光油门时机'])
            self.rev_avg = self.safe_mean(ld_df['开反推时间'])
            self.rev_dura_avg = self.safe_mean(ld_df['反推使用时长'])
            self.dist_td_max = self.safe_max(ld_df['接地点距离'])
            self.dist_40_max = self.safe_max(ld_df['入口至减速到40节距离'])
            self.head_ld_max = self.safe_max(ld_df['滑跑航向偏离'])
            self.roll_ld_max = self.safe_max(ld_df['着陆坡度'])

    def smart_replace(self, doc, replacements):
        """智能替换文档中的占位符，包括段落和表格"""
        pattern = re.compile(r'\{(\w+)\}')  # 匹配{word}格式

        # 处理段落文本
        for para in doc.paragraphs:
            if not para.text:
                continue

            new_text = para.text
            # 替换带花括号的参数
            for match in set(pattern.findall(para.text)):
                if match in replacements:
                    placeholder = f"{{{match}}}"
                    new_text = new_text.replace(placeholder, str(replacements[match]))

            # 替换不带花括号的参数
            for key, value in replacements.items():
                if key in new_text and f"{{{key}}}" not in para.text:
                    new_text = new_text.replace(key, str(value))

            if new_text != para.text:
                para.text = new_text

        # 处理表格文本
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    # 处理单元格中的段落
                    for para in cell.paragraphs:
                        if not para.text:
                            continue

                        new_text = para.text
                        # 替换带花括号的参数
                        for match in set(pattern.findall(para.text)):
                            if match in replacements:
                                placeholder = f"{{{match}}}"
                                new_text = new_text.replace(placeholder, str(replacements[match]))

                        # 替换不带花括号的参数
                        for key, value in replacements.items():
                            if key in new_text and f"{{{key}}}" not in para.text:
                                new_text = new_text.replace(key, str(value))

                        if new_text != para.text:
                            para.text = new_text

    def generate_word_report(self):
        try:
            doc = Document(os.path.join("Template", "C909越南运行QAR报告(模板).docx"))

            # === 第一阶段：处理内容替换和图表插入 ===
            replacements = {k: str(v) for k, v in self.prepare_replacements().items()}
            self.smart_replace(doc, replacements)

            chart_files = self.generate_chart_files()
            for pic_name, file_path in chart_files.items():
                for para in doc.paragraphs:
                    if f"{{{pic_name}}}" in para.text:
                        para.clear()  # 彻底清除段落内容
                        run = para.add_run()
                        run.add_picture(file_path, width=Inches(6))

            # === 第二阶段：格式设置 ===
            def set_font(run, font_name='仿宋_GB2312', font_size=None, bold=False):
                run.font.name = font_name
                run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
                if font_size:
                    run.font.size = Pt(font_size)
                run.bold = bold

            # 1. 全文档基础字体设置
            for paragraph in doc.paragraphs:
                for run in paragraph.runs:
                    set_font(run)  # 默认仿宋，不加粗

            # 2. 设置标题格式（一级和二级标题）
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                # 一级标题匹配：一、二、三、...
                if re.match(r'^[一二三四五六七八九十]+、', text):
                    for run in paragraph.runs:
                        set_font(run, font_size=16, bold=True)  # 三号字加粗
                # 二级标题匹配：1. 2. 3. ...
                elif re.match(r'^\d+\.', text):
                    for run in paragraph.runs:
                        set_font(run, font_size=16, bold=True)  # 三号字加粗

            # 3. 设置"三、昆岛起飞ROM测量分析"前所有内容为三号字
            set_small3_before = True
            for paragraph in doc.paragraphs:
                if "三、昆岛起飞ROM测量分析" in paragraph.text:
                    set_small3_before = False

                if set_small3_before:
                    for run in paragraph.runs:
                        run.font.size = Pt(16)  # 三号字

            # 4. 精确查找并设置"C909越南运行QAR.报"格式（改进版）
            target_pattern = re.compile(r'(C909越南运行QAR.报)')  # 精确匹配模式
            for paragraph in doc.paragraphs:
                if not target_pattern.search(paragraph.text):
                    continue

                # 保存原始段落属性
                original_alignment = paragraph.alignment

                # 重建段落内容
                new_paragraph = []
                text = paragraph.text
                last_end = 0

                for match in target_pattern.finditer(text):
                    # 添加匹配前的文本
                    if match.start() > last_end:
                        new_paragraph.append({
                            'text': text[last_end:match.start()],
                            'size': 16,  # 三号字
                            'bold': False
                        })

                    # 添加匹配到的文本
                    new_paragraph.append({
                        'text': match.group(1),
                        'size': 24,  # 小一
                        'bold': True
                    })

                    last_end = match.end()

                # 添加剩余文本
                if last_end < len(text):
                    new_paragraph.append({
                        'text': text[last_end:],
                        'size': 16,  # 三号字
                        'bold': False
                    })

                # 重新构建段落
                paragraph.clear()
                for part in new_paragraph:
                    run = paragraph.add_run(part['text'])
                    set_font(run)
                    run.font.size = Pt(part['size'])
                    run.bold = part['bold']

                # 恢复原始对齐方式
                paragraph.alignment = original_alignment

            # 3. 设置"三、昆岛起飞ROM测量分析"后的特殊格式
            rom_section_found = False
            for paragraph in doc.paragraphs:
                if "三、昆岛起飞ROM测量分析" in paragraph.text:
                    rom_section_found = True
                    continue

                if rom_section_found:
                    # 跳过标题段落（避免重复设置）
                    if re.match(r'^[一二三四五六七八九十]+、', paragraph.text) or re.match(r'^\d+\.',
                                                                                          paragraph.text):
                        continue

                    # 处理特殊注释段落
                    if paragraph.text.startswith("注：由于昆岛没有盲降"):
                        for run in paragraph.runs:
                            set_font(run, '楷体', 14)  # 四号楷体
                    # 普通正文段落
                    else:
                        for run in paragraph.runs:
                            set_font(run, font_size=15)  # 小三号字

            # 4. 设置表格字号（四号）
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                set_font(run, font_size=14)  # 四号字

            # === 第三阶段：确定保存路径和文件名 ===
            delta = self.end_date - self.start_date
            report_type = "月报" if delta.days >= 28 else "周报"
            save_dir = os.path.join("越南湿租报告", report_type)
            os.makedirs(save_dir, exist_ok=True)

            # 生成带日期范围的文件名
            start_str = self.start_date.strftime("%Y%m%d")
            end_str = self.end_date.strftime("%m%d")
            filename = f"C909越南运行QAR{report_type} {start_str}-{end_str}.docx"
            save_path = os.path.join(save_dir, filename)

            # 保存文档
            doc.save(save_path)

            # 清理临时文件
            for file_path in chart_files.values():
                if os.path.exists(file_path):
                    os.remove(file_path)

            # 仅保留这一个提示框（显示具体保存路径）
            messagebox.showinfo("文件保存位置", f"报告已保存到:\n{save_path}")

        except Exception as e:
            messagebox.showerror("错误", f"报告生成失败:\n{str(e)}")
            raise

    def prepare_replacements(self):
        """准备替换字典，确保所有值都是基本Python类型"""

        def convert_value(value):
            """将numpy类型转换为Python基本类型"""
            if hasattr(value, 'item'):
                return value.item()
            return value

        # 筛选指定机场的数据
        to_df = self.df[self.df['起飞机场'] == 'VVCS']  # 起飞数据
        ld_df = self.df[self.df['着陆机场'] == 'VVCS']  # 着陆数据

        params = {
            # 基本参数
            'monthly_weekly': str(self.monthly_weekly),
            'month_s': str(self.month_s),
            'day_s': str(self.day_s),
            'month_e': self.month_e,
            'day_e': self.day_e,
            'flight_num': int(self.flight_num),
            'b_652g_num': int(self.b_652g_num),
            'b_656E_num': int(self.b_656E_num),
            'wqar_num': int(self.wqar_num),

            # 起飞参数（VVCS起飞的）
            'gw_to_avg': round(float(self.gw_to_avg), 1),
            'wind_tail_to_avg': round(float(self.wind_tail_to_avg), 1),
            'wind_cross_to_avg': round(float(self.wind_cross_to_avg), 1),
            'dist_to_avg': int(round(float(self.dist_to_avg))),
            'vr_to_avg': round(float(self.vr_to_avg), 1),
            'pitch_to_avg': round(float(self.pitch_to_avg), 1),
            'pitch_r_to_max': round(float(self.pitch_r_to_max), 1),
            'pitch_r_to_avg': round(float(self.pitch_r_to_avg), 1),
            'head_to_avg': round(float(self.head_to_avg), 1),
            'roll_to_avg': round(float(self.roll_to_avg), 1),
            'head_to_max': round(float(self.head_to_max), 1),
            'roll_to_max': round(float(self.roll_to_max), 1),
            'dist_to_max': int(round(float(self.dist_to_max))),
            'wind_to_avg': round(float(self.wind_tail_to_avg), 1),
            'wind_to_max': round(float(to_df['起飞平均顺风'].max()), 1) if not to_df.empty else 0.0,
            'wind_to_min': round(float(to_df['起飞平均顺风'].min()), 1) if not to_df.empty else 0.0,

            # 着陆参数（VVCS着陆的）
            'gw_ld_avg': round(float(self.gw_ld_avg), 1),
            'fuel_ld_avg': round(float(self.fuel_ld_avg), 1),
            'wind_tail_ld': round(float(self.wind_tail_ld), 1),
            'wind_cross_ld': round(float(self.wind_cross_ld), 1),
            'alt_stab_avg': int(round(float(self.alt_stab_avg))),
            'alt_thr_avg': round(float(self.alt_thr_avg), 1),
            'alt_s_flare_avg': round(float(self.alt_s_flare_avg), 1),
            'alt_flare_avg': round(float(self.alt_flare_avg), 1),
            'dist_td_avg': int(round(float(self.dist_td_avg))),
            'dist_40_avg': int(round(float(self.dist_40_avg))),
            'vapp_dev_avg': round(float(self.vapp_dev_avg), 1),
            'pitch_ld_avg': round(float(self.pitch_ld_avg), 1),
            'vrtg_avg': round(float(self.vrtg_avg), 3),
            'roll_ld_avg': round(float(self.roll_ld_avg), 1),
            'head_ld_avg': round(float(self.head_ld_avg), 1),
            'thr_avg': round(float(self.thr_avg), 1),
            'rev_avg': round(float(self.rev_avg), 1),
            'rev_dura_avg': round(float(self.rev_dura_avg), 1),
            'dist_td_max': int(round(float(self.dist_td_max))),
            'dist_40_max': int(round(float(self.dist_40_max))),
            'head_ld_max': round(float(self.head_ld_max), 1),
            'roll_ld_max': round(float(self.roll_ld_max), 1),
            'wind_ld_avg': round(float(self.wind_tail_ld), 1),
            'wind_ld_max': round(float(ld_df['着陆平均顺风'].max()), 1) if not ld_df.empty else 0.0,
            'wind_ld_min': round(float(ld_df['着陆平均顺风'].min()), 1) if not ld_df.empty else 0.0,
            'alt_thr_avg': round(float(self.alt_thr_avg), 1),
            'alt_thr_max': round(float(ld_df['进跑道高度'].max()), 1) if not ld_df.empty else 0.0,
            'alt_thr_min': round(float(ld_df['进跑道高度'].min()), 1) if not ld_df.empty else 0.0,
            'vrtg_ld_avg': round(float(self.vrtg_avg), 3),
            'vrtg_ld_max': round(float(ld_df['着陆载荷'].max()), 3) if not ld_df.empty else 0.0,
            'vrtg_ld_min': round(float(ld_df['着陆载荷'].min()), 3) if not ld_df.empty else 0.0,
            'rev_ld_avg': round(float(self.rev_dura_avg), 1),
            'rev_ld_max': int(round(float(ld_df['反推使用时长'].max()))) if not ld_df.empty else 0.0,
            'rev_ld_min': int(round(float(ld_df['反推使用时长'].min()))) if not ld_df.empty else 0.0,

            # 比较参数
            'dist_to_add': "0",
            'dist_td_add': "0",
            'dist_40_add': "0"
        }
        return params

    def generate_chart_files(self):
        """生成所有图表文件，并调整曲线与图示的间距"""
        chart_files = {}

        # 起飞相关图表
        to_df = self.df[self.df['起飞机场'] == 'VVCS']
        if not to_df.empty:
            # 计算起飞平均顺风的纵轴范围
            wind_min = to_df['起飞平均顺风'].min()
            wind_max = to_df['起飞平均顺风'].max()
            wind_y_lim = (wind_min - 1, wind_max + 1)

            chart_files.update({
                'pic_dist_to': self.create_chart_with_standards(
                    to_df, '起飞日期', '起飞滑跑距离',
                    "起飞滑跑距离(米)", (600, 1820),
                    [(1200, "orange", "关注门限"), (1350, "red", "警戒门限"), (1820, "black", "跑道长度")]
                ),
                'pic_head_to': self.create_chart_with_standards(
                    to_df, '起飞日期', '起飞滑跑航向偏离',
                    "起飞滑跑航向偏离(度)", (0, 5),
                    [(4, "orange", "关注门限"), (5, "red", "警戒门限")]
                ),
                'pic_roll_to': self.create_chart_with_standards(
                    to_df, '起飞日期', '起飞最大坡度',
                    "起飞坡度(度)", (0, 6),
                    [(5, "orange", "关注门限"), (6, "red", "警戒门限")]
                ),
                'pic_wind_to': self.create_wind_chart(to_df)  # 单独处理顺风图表
            })

        # 着陆相关图表
        ld_df = self.df[self.df['着陆机场'] == 'VVCS']
        if not ld_df.empty:
            # 计算着陆减速至40节距离的纵轴下限
            dist_40_min = ld_df['入口至减速到40节距离'].min()
            dist_40_min = (dist_40_min // 100) * 100  # 除以100取整再乘以100

            chart_files.update({
                'pic_dist_ld': self.create_chart_with_standards(
                    ld_df, '起飞日期', '接地点距离',
                    "接地点距离(米)", (200, 600),
                    [(550, "orange", "关注门限"), (600, "red", "警戒门限（决策点）"),
                    (250, "orange", ""), (200, "red", "")]
                ),
                'pic_dist_40': self.create_chart_with_standards(
                    ld_df, '起飞日期', '入口至减速到40节距离',
                    "着陆减速至40节距离(米)", (dist_40_min, 1820),
                    [(1650, "orange", "关注门限"),(1750, "red", "预警门限"), (1820, "black", "跑道长度")]
                ),
                'pic_head_ld': self.create_chart_with_standards(
                    ld_df, '起飞日期', '滑跑航向偏离',
                    "着陆滑跑航向偏离(度)", (0, 5),
                    [(4, "orange", "关注门限"), (5, "red", "警戒门限")]
                ),
                'pic_roll_ld': self.create_chart_with_standards(
                    ld_df, '起飞日期', '着陆坡度',
                    "着陆坡度(度)", (0, 6),
                    [(4, "orange", "关注门限"), (6, "red", "警戒门限")]
                )
            })
            # 1. 着陆平均顺风图表
            chart_files['pic_wind_ld'] = self.create_wind_chart(ld_df, '着陆平均顺风', "着陆平均顺风(节)")

            # 2. 进跑道高度图表
            chart_files['pic_alt_thr'] = self.create_chart_with_standards(
                ld_df, '起飞日期', '进跑道高度',
                "进跑道高度(英尺)", (25, 65),
                [(35, "orange", "关注门限"), (30, "red", "警戒门限"),
                 (55, "orange", ""), (60, "red", "")]
            )

            # 3. 着陆载荷图表
            vrtg_max = ld_df['着陆载荷'].max()
            vrtg_upper = (int(vrtg_max * 10) + 1) / 10  # 最大值*10，取整，+1，再除以10
            vrtg_upper = vrtg_upper if vrtg_upper > 1.8 else 1.8  # 判断并取值

            chart_files['pic_vrtg_ld'] = self.create_chart_with_standards(
                ld_df, '起飞日期', '着陆载荷',
                "着陆载荷(g)", (1.0, vrtg_upper),
                [(1.7, "orange", "关注门限"), (1.8, "red", "警戒门限")]
            )

            # 4. 反推使用时长图表
            chart_files['pic_rev_ld'] = self.create_chart(
                ld_df, '起飞日期', '反推使用时长',
                "反推使用时长(秒)",
                (0, ld_df['反推使用时长'].max() * 1.1)  # 上限为最大值的1.1倍
            )

        return chart_files

    def create_wind_chart(self, df, y_col=None, y_label=None):
        """创建顺风图表，兼容旧代码和新需求"""
        if y_col is None and y_label is None:
            # 旧代码调用方式，处理起飞平均顺风
            y_col = '起飞平均顺风'
            y_label = "起飞平均顺风(节)"

        # 计算纵轴范围
        wind_min = df[y_col].min()
        wind_max = df[y_col].max()

        # 新逻辑：调整纵坐标范围
        if wind_max < 0:
            y_lim = (wind_min - 1, 1)  # 最大值小于0时，上限设为1
        elif wind_min > 0:
            y_lim = (-1, wind_max + 1)  # 最小值大于0时，下限设为-1
        else:
            y_lim = (wind_min - 1, wind_max + 1)  # 其他情况保持原逻辑

        # 绘制基础图表
        x, y, dates = self._draw_base_chart(df, '起飞日期', y_col, y_label, y_lim)

        # 添加0线
        zero_line = plt.axhline(y=0, color='orange', linestyle='-', linewidth=2.5, zorder=3)

        # 获取当前坐标轴范围
        ax = plt.gca()
        xlim = ax.get_xlim()

        # 添加文字标注
        plt.text(xlim[0] + 0.02 * (xlim[1] - xlim[0]), 0.5, '顺风',
                 color='orange', fontsize=16,
                 verticalalignment='bottom', horizontalalignment='left',
                 bbox=dict(facecolor='white', alpha=0.8, edgecolor='none', pad=0))

        plt.text(xlim[0] + 0.02 * (xlim[1] - xlim[0]), -0.5, '顶风',
                 color='green', fontsize=16,
                 verticalalignment='top', horizontalalignment='left',
                 bbox=dict(facecolor='white', alpha=0.8, edgecolor='none', pad=0))

        # 移除图例
        ax.legend_.remove() if ax.legend_ else None

        # 调整图表边距
        plt.subplots_adjust(left=0.1, bottom=0.15)

        return self._save_chart(y_col)

    def create_chart(self, df, x_col, y_col, y_label, y_lim, red_line=None, red_label=None):
        """统一图表生成方法（调整了曲线与图示的间距）"""
        # 1. 绘制基础图表
        x, y, dates = self._draw_base_chart(df, x_col, y_col, y_label, y_lim)

        # 2. 添加参考线
        avg = df[y_col].mean()
        plt.axhline(y=avg, color='green', linestyle='-', linewidth=2.5,
                    label=f'平均值: {avg:.1f}', zorder=3)

        if red_line is not None:
            plt.axhline(y=red_line, color='red', linestyle='-',
                        linewidth=3.0, label=red_label, zorder=3)

        # 3. 调整图例位置和间距
        n_items = 2 if red_line is None else 3
        plt.legend(
            prop={'weight': 'bold', 'size': 14},  # 缩小图例字体
            loc='upper center',
            bbox_to_anchor=(0.5, -0.12),  # 上移图例
            ncol=min(4, 2 + n_items),
            framealpha=0.8
        )

        # 4. 调整图表底部间距（关键修改）
        plt.subplots_adjust(bottom=0.18)  # 减小底部空白

        return self._save_chart(y_col)

    def create_chart_with_standards(self, df, x_col, y_col, y_label, y_lim, standards):
        """带标准线的图表生成方法（调整间距）"""
        # 1. 绘制基础图表
        x, y, dates = self._draw_base_chart(df, x_col, y_col, y_label, y_lim)

        # 2. 添加参考线
        avg = df[y_col].mean()
        plt.axhline(y=avg, color='green', linestyle='-', linewidth=2.5,
                    label=f'平均值: {avg:.1f}', zorder=3)

        for val, color, label in standards:
            linewidth = 3.0 if color.lower() in ['red', 'orange'] else 2.5
            plt.axhline(y=val, color=color, linestyle='-',
                        linewidth=linewidth, label=label, zorder=3)

        # 3. 调整图例位置和间距
        n_standards = len(standards)
        plt.legend(
            prop={'weight': 'bold', 'size': 14},  # 缩小图例字体
            loc='upper center',
            bbox_to_anchor=(0.5, -0.12),  # 上移图例
            ncol=min(4, 2 + n_standards),
            framealpha=0.8
        )

        # 4. 调整图表底部间距（关键修改）
        plt.subplots_adjust(bottom=0.15 + 0.02 * n_standards)  # 减小底部空白

        temp_file = os.path.join(tempfile.gettempdir(), f"temp_{y_col}_std.png")
        plt.savefig(temp_file, dpi=300, bbox_inches='tight')
        plt.close()
        return temp_file

    def _draw_base_chart(self, df, x_col, y_col, y_label, y_lim, figsize=(12, 6)):
        """基础图表绘制方法(内部使用)"""
        plt.figure(figsize=figsize)

        # 1. 先设置全局样式（包括边框默认黑色）
        plt.rcParams.update({
            'font.size': 14,
            'axes.titlesize': 14,
            'axes.labelsize': 13,
            'xtick.labelsize': 14,
            'ytick.labelsize': 14,
            'legend.fontsize': 14,
            'legend.title_fontsize': 14,
            'axes.edgecolor': 'black',  # 默认所有边框为黑色
            'axes.linewidth': 0.8
        })

        # 数据处理
        df = df.sort_values(by=[x_col, '起飞时间'])
        dates = df[x_col].unique()
        x, x_ticks = [], []
        for i, date in enumerate(dates):
            same_date = df[df[x_col] == date]
            n = len(same_date)

            if n == 1:
                x.append(i)
                x_ticks.append(i)
            else:
                for j in range(n):
                    pos = i - 0.4 + j * (0.8 / (n - 1)) if n > 1 else i
                    x.append(pos)
                x_ticks.append(i)

        # 绘制数据线
        y = df[y_col].values
        plt.plot(x, y, 'b-', linewidth=2.5)
        plt.plot(x, y, 'bo')

        # 设置图表基本属性
        date_labels = [d.strftime('%m-%d') for d in dates]

        # 如果是月报且日期较多，调整横轴间隔
        if self.monthly_weekly == "月" and len(dates) > 10:
            step = max(1, len(dates) // 10)  # 确保至少显示10个标签
            plt.xticks(x_ticks[::step], date_labels[::step])
        else:
            plt.xticks(x_ticks, date_labels)

        plt.ylim(y_lim)
        plt.ylabel(y_label, fontsize=13, fontweight='bold')
        plt.xlabel('')
        plt.title(y_label, fontsize=16, fontweight='bold')

        # 设置网格和边框
        ax = plt.gca()
        ax.grid(True, color='lightgray', linestyle='-', linewidth=0.5)

        # 仅修改上边框为灰色（其他边保持默认黑色）
        ax.spines['top'].set_color('lightgray')

        return x, y, dates

    def _set_chart_legend(self, n_items):
        """统一设置图例样式"""
        plt.legend(
            prop={'weight': 'bold', 'size': 14},
            loc='upper center',
            bbox_to_anchor=(0.5, -0.12),
            ncol=min(4, max(2, n_items)),
            framealpha=0.8
        )
        plt.subplots_adjust(bottom=0.15 + 0.03 * n_items)

    def _save_chart(self, file_suffix):
        """统一保存图表"""
        temp_file = os.path.join(tempfile.gettempdir(), f"temp_{file_suffix}.png")
        plt.savefig(temp_file, dpi=300, bbox_inches='tight')
        plt.close()
        return temp_file

if __name__ == "__main__":
    try:
        ReportGenerator()
    except Exception as e:
        messagebox.showerror("错误", f"程序运行失败: {str(e)}")