import os
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import docx
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Alignment
from openpyxl.utils import get_column_letter
from tkcalendar import DateEntry
import shutil
import threading
import time


class DateRangeSelector:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("越南湿租报告处理程序")
        self.root.geometry("520x480")
        self.root.resizable(False, False)

        # 设置窗口居中
        self.center_window()

        self.selected_dates = None
        self.start_cal = None
        self.end_cal = None

        # 进度相关变量
        self.is_processing = False
        self.start_time = None
        self.total_files = 0
        self.processed_files = 0
        self.current_file = ""

        # 进度显示组件
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="请选择日期范围后点击开始处理")
        self.time_var = tk.StringVar(value="")
        self.file_progress_var = tk.StringVar(value="")
        self.result_var = tk.StringVar(value="")

        self.create_widgets()

    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = 520
        height = 480
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        # 主框架 - 减少padding
        main_frame = ttk.Frame(self.root, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 日期选择部分
        date_frame = ttk.LabelFrame(main_frame, text="📅 日期范围选择", padding=8)
        date_frame.pack(fill=tk.X, pady=(0, 8))

        # 使用Grid布局优化日期选择
        ttk.Label(date_frame, text="开始日期:", font=('Arial', 9)).grid(row=0, column=0, padx=5, pady=3, sticky="e")
        self.start_cal = DateEntry(date_frame, date_pattern='yyyy-mm-dd', font=('Arial', 9), width=12)
        self.start_cal.grid(row=0, column=1, padx=5, pady=3, sticky="ew")

        ttk.Label(date_frame, text="结束日期:", font=('Arial', 9)).grid(row=1, column=0, padx=5, pady=3, sticky="e")
        self.end_cal = DateEntry(date_frame, date_pattern='yyyy-mm-dd', font=('Arial', 9), width=12)
        self.end_cal.grid(row=1, column=1, padx=5, pady=3, sticky="ew")

        date_frame.columnconfigure(1, weight=1)

        # 设置默认日期（2025年4月19日到今天）
        self.set_default_dates()

        # 快速选择按钮 - 减少padding
        quick_select_frame = ttk.LabelFrame(main_frame, text="⚡ 快速选择", padding=8)
        quick_select_frame.pack(fill=tk.X, pady=(0, 8))

        btn_frame = ttk.Frame(quick_select_frame)
        btn_frame.pack(fill=tk.X)

        ttk.Button(btn_frame, text="最近7天", command=self.set_last_7_days).pack(
            side=tk.LEFT, padx=3, fill=tk.X, expand=True)
        ttk.Button(btn_frame, text="最近1月", command=self.set_last_30_days).pack(
            side=tk.LEFT, padx=3, fill=tk.X, expand=True)
        ttk.Button(btn_frame, text="所有数据", command=self.set_all_data).pack(
            side=tk.LEFT, padx=3, fill=tk.X, expand=True)

        # 进度显示部分 - 优化布局
        progress_frame = ttk.LabelFrame(main_frame, text="📊 处理进度", padding=8)
        progress_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 8))

        # 当前状态
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var,
                                     font=('Arial', 9), foreground='#2E86AB')
        self.status_label.pack(fill=tk.X, pady=(0, 5))

        # 文件进度显示 【n/m】
        self.file_progress_label = ttk.Label(progress_frame, textvariable=self.file_progress_var,
                                           font=('Arial', 10, 'bold'), foreground='#E63946')
        self.file_progress_label.pack(fill=tk.X, pady=(0, 5))

        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, style='TProgressbar')
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))

        # 时间信息
        self.time_label = ttk.Label(progress_frame, textvariable=self.time_var,
                                   font=('Arial', 8), foreground='#6C757D')
        self.time_label.pack(fill=tk.X, pady=(0, 5))

        # 结果显示区域 - 利用时间下方的空白区域
        self.result_label = ttk.Label(progress_frame, textvariable=self.result_var,
                                     font=('Arial', 9, 'bold'), foreground='#28A745',
                                     wraplength=450, justify='center')
        self.result_label.pack(fill=tk.X, pady=(5, 0))

        # 操作按钮 - 更大更醒目
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))

        # 退出按钮
        ttk.Button(button_frame, text="退出程序", command=self.root.destroy).pack(side=tk.LEFT)

        # 开始处理按钮 - 更醒目
        self.start_button = ttk.Button(button_frame, text="🚀 开始处理", command=self.start_processing)
        self.start_button.pack(side=tk.RIGHT, ipadx=10)

    def set_default_dates(self):
        """设置默认日期：2025年4月19日到今天"""
        self.start_cal.set_date(datetime(2025, 4, 19).date())
        self.end_cal.set_date(datetime.now().date())

    def set_last_7_days(self):
        today = datetime.now().date()
        self.start_cal.set_date(today - timedelta(days=7))
        self.end_cal.set_date(today)

    def set_last_30_days(self):
        today = datetime.now().date()
        self.start_cal.set_date(today - timedelta(days=30))
        self.end_cal.set_date(today)

    def set_all_data(self):
        self.start_cal.set_date(datetime(2025, 4, 19).date())
        self.end_cal.set_date(datetime.now().date())

    def start_processing(self):
        """开始处理按钮的回调函数"""
        if self.is_processing:
            return

        start_date = self.start_cal.get_date()
        end_date = self.end_cal.get_date()

        if start_date > end_date:
            messagebox.showerror("错误", "结束日期不能早于开始日期")
            return

        self.selected_dates = (start_date, end_date)
        self.is_processing = True
        self.start_button.config(state='disabled', text='⏳ 处理中...')

        # 清空之前的结果显示，重置颜色为绿色
        self.result_var.set("")
        self.result_label.config(foreground='#28A745')

        # 在新线程中处理文件，避免界面卡死
        threading.Thread(target=self.process_files, daemon=True).start()

    def update_progress(self, current, total, current_file="", file_exists=True):
        """更新进度显示"""
        if total > 0:
            progress = (current / total) * 100
            self.progress_var.set(progress)

        # 更新文件进度显示 【n/m】格式
        if total > 0:
            self.file_progress_var.set(f"【{current}/{total}】")

        if current_file:
            self.current_file = current_file
            if file_exists:
                self.status_var.set(f"正在处理: {current_file}")
            else:
                self.status_var.set(f"文件不存在: {current_file}")

        # 更新时间信息
        if self.start_time:
            elapsed = time.time() - self.start_time
            elapsed_str = f"已用时间: {int(elapsed//60)}分{int(elapsed%60)}秒"

            if current > 0 and total > 0:
                avg_time = elapsed / current
                remaining = (total - current) * avg_time
                remaining_str = f"预估剩余: {int(remaining//60)}分{int(remaining%60)}秒"
                self.time_var.set(f"{elapsed_str} | {remaining_str}")
            else:
                self.time_var.set(elapsed_str)

        # 强制更新界面
        self.root.update_idletasks()

    def process_files(self):
        """在后台线程中处理文件"""
        try:
            date_start, date_end = self.selected_dates
            self.start_time = time.time()

            # 准备文件路径
            current_dir = os.getcwd()
            word_dir = os.path.join(current_dir, "越南湿租报告", "日报（王芃）")
            template_path = os.path.join(current_dir, "Template", "Vietnam_Template.xlsx")
            output_path = os.path.join(current_dir, "越南湿租报告", "Vietnam_Report.xlsx")

            # 检查路径
            if not all(os.path.exists(p) for p in [word_dir, template_path]):
                raise Exception("必要的目录或模板文件不存在")

            # 准备输出文件
            shutil.copy(template_path, output_path)

            # 计算总文件数
            total_days = (date_end - date_start).days + 1
            self.total_files = total_days
            self.processed_files = 0

            self.root.after(0, lambda: self.status_var.set(f"开始处理 {total_days} 天的数据..."))

            # 处理文件
            current_date = date_start
            success_count = 0

            while current_date <= date_end:
                date_str = current_date.strftime("%Y%m%d")
                word_file = f"C909越南运行QAR日报_王总_{date_str}.docx"
                word_path = os.path.join(word_dir, word_file)

                self.processed_files += 1

                if os.path.exists(word_path):
                    try:
                        # 更新进度
                        self.root.after(0, lambda f=word_file: self.update_progress(
                            self.processed_files, self.total_files, f, True))

                        process_word_file(word_path, output_path)
                        success_count += 1

                    except Exception as e:
                        print(f"处理 {word_file} 时出错: {str(e)}")
                else:
                    # 更新进度（文件不存在）
                    self.root.after(0, lambda f=word_file: self.update_progress(
                        self.processed_files, self.total_files, f, False))

                current_date += timedelta(days=1)
                time.sleep(0.1)  # 短暂延迟，让界面有时间更新

            # 处理完成
            self.root.after(0, self.processing_completed, success_count, output_path)

        except Exception as e:
            self.root.after(0, lambda: self.processing_error(str(e)))

    def processing_completed(self, success_count, output_path):
        """处理完成的回调"""
        self.is_processing = False
        self.start_button.config(state='normal', text='🚀 开始处理')
        self.progress_var.set(100)
        self.status_var.set(f"✅ 处理完成！")
        self.file_progress_var.set(f"【{self.total_files}/{self.total_files}】")

        elapsed = time.time() - self.start_time
        self.time_var.set(f"总用时: {int(elapsed//60)}分{int(elapsed%60)}秒")

        # 在主窗体上显示完成结果，而不是弹窗
        result_text = f"🎉 处理完成！\n✅ 成功处理 {success_count} 个文件\n📁 结果保存在: {output_path}"
        self.result_var.set(result_text)

    def processing_error(self, error_msg):
        """处理错误的回调"""
        self.is_processing = False
        self.start_button.config(state='normal', text='🚀 开始处理')
        self.status_var.set(f"❌ 处理出错")
        self.file_progress_var.set("")

        # 在主窗体上显示错误信息，而不是弹窗
        self.result_var.set(f"❌ 处理过程中出现错误:\n{error_msg}")
        # 错误信息用红色显示
        self.result_label.config(foreground='#DC3545')

    def run(self):
        """运行主窗口"""
        self.root.mainloop()


def save_workbook(wb, filepath):
    try:
        wb.save(filepath)
    except Exception as e:
        print(f"保存失败: {str(e)}")
        try:
            new_wb = Workbook()
            new_ws = new_wb.active
            new_ws.title = wb.active.title

            for row in wb.active.iter_rows():
                for cell in row:
                    new_cell = new_ws[cell.coordinate]
                    new_cell.value = cell.value
                    if cell.has_style:
                        new_cell.font = cell.font.copy()
                        new_cell.border = cell.border.copy()
                        new_cell.fill = cell.fill.copy()
                        new_cell.number_format = cell.number_format
                        new_cell.protection = cell.protection.copy()
                        new_cell.alignment = cell.alignment.copy()

            for col in wb.active.columns:
                col_letter = get_column_letter(col[0].column)
                new_ws.column_dimensions[col_letter].width = wb.active.column_dimensions[col_letter].width

            new_wb.save(filepath)
        except Exception as e:
            raise Exception(f"无法保存工作簿: {str(e)}")


def auto_adjust_column_width(ws):
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            try:
                cell_length = sum(2 if ord(c) > 256 else 1 for c in str(cell.value))
                if cell_length > max_length:
                    max_length = cell_length
            except:
                pass
        adjusted_width = (max_length + 1) * 1.05
        ws.column_dimensions[column].width = min(adjusted_width, 50)


def format_excel(ws):
    for col in range(8, 38):
        col_letter = get_column_letter(col)
        for cell in ws[col_letter]:
            if cell.row == 1:
                continue
            try:
                if cell.value is not None and str(cell.value).strip():
                    try:
                        cell.value = float(cell.value)
                    except (ValueError, TypeError):
                        pass
            except AttributeError:
                pass

    for row in ws.iter_rows():
        for cell in row:
            cell.alignment = Alignment(horizontal='center', vertical='center')
            if cell.value is None:
                cell.value = ""

    auto_adjust_column_width(ws)


def find_first_empty_row(ws):
    row = 2
    while ws.cell(row=row, column=1).value is not None:
        row += 1
    return row


def find_matching_row(ws, data):
    key_fields = ["飞机号", "起飞机场", "着陆机场", "航班号", "起飞日期"]
    headers = [cell.value for cell in ws[1] if cell.value]

    for row in range(2, ws.max_row + 1):
        match = True
        for field in key_fields:
            if field not in headers:
                match = False
                break

            col = headers.index(field) + 1
            cell_value = str(ws.cell(row=row, column=col).value or "").strip()
            data_value = str(data.get(field, "")).strip()

            if cell_value != data_value:
                match = False
                break

        if match:
            return row

    return None


def process_table(ws, table, aircraft, file_date, is_landing):
    headers = [cell.value for cell in ws[1] if cell.value]

    for col_idx in range(3, len(table.columns)):
        try:
            col_cells = [row.cells[col_idx] for row in table.rows if len(row.cells) > col_idx]

            if not col_cells or not col_cells[0].text.strip():
                continue

            flight_number = col_cells[0].text.strip()
            airports = col_cells[1].text.strip() if len(col_cells) > 1 else ""

            if len(airports) < 8:
                continue

            departure_airport = airports[:4]
            arrival_airport = airports[-4:]

            data = {
                "飞机号": aircraft,
                "起飞机场": departure_airport,
                "着陆机场": arrival_airport,
                "航班号": flight_number,
                "起飞日期": file_date
            }

            if not is_landing:
                field_names = [
                    "起飞时间", "起飞重量", "起飞燃油", "起飞平均顺风", "起飞最大侧风",
                    "起飞滑跑距离", "抬轮与VR时差", "离地姿态", "抬轮最大速率",
                    "抬轮平均速率", "起飞滑跑航向偏离", "起飞最大坡度"
                ]

                for i, field in enumerate(field_names):
                    if i + 2 < len(col_cells):
                        data[field] = col_cells[i + 2].text.strip()

                new_row = find_first_empty_row(ws)
                for field, value in data.items():
                    if field in headers:
                        col = headers.index(field) + 1
                        ws.cell(row=new_row, column=col, value=value)
            else:
                field_names = [
                    "落地时间", "着陆重量", "着陆剩余燃油", "着陆构型", "着陆平均顺风",
                    "着陆最大侧风", "稳定进近高度", "进跑道高度", "拉开始高度",
                    "接近拉平高度", "接地点距离", "入口至减速到40节距离", "进近速度偏差",
                    "着陆姿态", "着陆载荷", "着陆坡度", "滑跑航向偏离", "收光油门时机",
                    "开反推时间", "反推使用时长"
                ]

                for i, field in enumerate(field_names):
                    if i + 2 < len(col_cells):
                        data[field] = col_cells[i + 2].text.strip()

                matching_row = find_matching_row(ws, data)
                if matching_row:
                    for field, value in data.items():
                        if field in headers:
                            col = headers.index(field) + 1
                            ws.cell(row=matching_row, column=col, value=value)

        except Exception as e:
            print(f"处理表格列时出错: {str(e)}")
            continue


def process_word_file(word_file_path, excel_file):
    try:
        doc = docx.Document(word_file_path)
    except Exception as e:
        raise Exception(f"无法打开Word文件: {str(e)}")

    try:
        file_date = os.path.basename(word_file_path).split("_")[-1].split(".")[0]
        formatted_date = datetime.strptime(file_date, "%Y%m%d").strftime("%Y-%m-%d")
    except:
        raise Exception("无法从文件名提取日期")

    if len(doc.tables) < 4:
        raise Exception(f"文件需要4个表格，但只找到 {len(doc.tables)} 个")

    try:
        wb = load_workbook(excel_file)
        ws = wb.active

        process_table(ws, doc.tables[0], "B-652G", formatted_date, is_landing=False)
        process_table(ws, doc.tables[1], "B-656E", formatted_date, is_landing=False)
        process_table(ws, doc.tables[2], "B-652G", formatted_date, is_landing=True)
        process_table(ws, doc.tables[3], "B-656E", formatted_date, is_landing=True)

        format_excel(ws)
        save_workbook(wb, excel_file)

    except Exception as e:
        raise Exception(f"处理Word文件时出错: {str(e)}")
    finally:
        if 'wb' in locals():
            wb.close()


def main():
    """主函数 - 启动GUI应用程序"""
    try:
        # 创建并运行主窗口
        app = DateRangeSelector()
        app.run()
    except Exception as e:
        print(f"程序启动错误: {str(e)}")
        messagebox.showerror("启动错误", f"程序启动时出现错误:\n{str(e)}")


if __name__ == "__main__":
    main()
