import os
import shutil
from tkinter import *
from tkinter import ttk, messagebox
from datetime import datetime, date, timedelta
import tkcalendar as tkcal
import time
import threading

# 配置参数
LOCAL_PATH = r"D:\AirFASE\FIMRoot"  # 本地存储路径
PATH1 = r"Z:\DATA_BAK\FDIMU_PC"  # 数据源路径
TEMP_PATH = r"D:\FIM_temp"  # 临时文件夹路径

# 飞机型号映射
AIRCRAFT_TYPE_MAP = {
    # A319
    "B-1011": "A319", "B-1093": "A319", "B-6163": "A319",
    "B-6229": "A319", "B-6230": "A319", "B-8852": "A319",
    "B-8853": "A319",

    # A320
    "B-1856": "A320", "B-6728": "A320", "B-6729": "A320",
    "B-6730": "A320", "B-6850": "A320", "B-6900": "A320",
    "B-6907": "A320", "B-6940": "A320", "B-9985": "A320",

    # A320S
    "B-1071": "A320S", "B-1095": "A320S", "B-1630": "A320S",
    "B-1631": "A320S", "B-1632": "A320S", "B-1633": "A320S",
    "B-30CP": "A320S", "B-30CQ": "A320S", "B-30DW": "A320S",
    "B-30DX": "A320S", "B-321Y": "A320S", "B-323X": "A320S",
    "B-329W": "A320S", "B-329X": "A320S", "B-32AE": "A320S",
    "B-32AF": "A320S", "B-32D7": "A320S", "B-8162": "A320S",
    "B-8185": "A320S", "B-8186": "A320S", "B-8342": "A320S",
    "B-8345": "A320S", "B-8606": "A320S", "B-8607": "A320S",
    "B-8608": "A320S", "B-8609": "A320S", "B-8610": "A320S",
    "B-8875": "A320S", "B-8876": "A320S", "B-8877": "A320S",
    "B-8878": "A320S", "B-8879": "A320S",

    # A320N
    "B-32D8": "A320N", "B-32DC": "A320N", "B-32EQ": "A320N",
    "B-32ER": "A320N", "B-32ES": "A320N", "B-32EX": "A320N",
    "B-32FA": "A320N", "B-32GV": "A320N", "B-32GW": "A320N",
    "B-32HC": "A320N", "B-32HL": "A320N", "B-32M2": "A320N",

    # A321N
    "B-32F9": "A321N", "B-32G5": "A321N", "B-32GM": "A321N",
    "B-32GT": "A321N", "B-32GU": "A321N",

    # ARJ21
    "B-104X": "ARJ21", "B-3321": "ARJ21",
    "B-3322": "ARJ21", "B-3328": "ARJ21", "B-3329": "ARJ21",
    "B-3386": "ARJ21", "B-3387": "ARJ21", "B-3388": "ARJ21",
    "B-602A": "ARJ21", "B-602C": "ARJ21", "B-603M": "ARJ21",
    "B-603N": "ARJ21", "B-603P": "ARJ21", "B-603Q": "ARJ21",
    "B-603W": "ARJ21", "B-603Z": "ARJ21", "B-604A": "ARJ21",
    "B-604C": "ARJ21", "B-604D": "ARJ21", "B-604F": "ARJ21",
    "B-605M": "ARJ21", "B-605N": "ARJ21", "B-620D": "ARJ21",
    "B-620E": "ARJ21", "B-650S": "ARJ21", "B-650T": "ARJ21",
    "B-651M": "ARJ21", "B-651P": "ARJ21", "B-651Q": "ARJ21",
    "B-651R": "ARJ21", "B-651S": "ARJ21", "B-651T": "ARJ21",
    "B-652F": "ARJ21", "B-652G": "ARJ21", "B-653E": "ARJ21",
    "B-656C": "ARJ21", "B-656D": "ARJ21", "B-656E": "ARJ21",
    "B-658A": "ARJ21", "B-658C": "ARJ21"
}

# 制造商映射
MANUFACTURER_MAP = {
    "A319": "AIRBUS", "A319S": "AIRBUS", "A320": "AIRBUS", "A320S": "AIRBUS",
    "A320N": "AIRBUS", "A321": "AIRBUS", "A321N": "AIRBUS", "A330": "AIRBUS",
    "A350": "AIRBUS", "ARJ21": "COMAC", "C909": "COMAC", "C919": "COMAC",
    "B737": "BOEING"
}


class AircraftSelector:
    """飞机号选择器类"""
    def __init__(self, parent, aircraft_list):
        self.parent = parent
        self.aircraft_list = aircraft_list
        self.selected_aircraft = set(aircraft_list)  # 默认全选
        self.aircraft_vars = {}

        self.create_widgets()

    def create_widgets(self):
        # 清空父容器
        for widget in self.parent.winfo_children():
            widget.destroy()

        if not self.aircraft_list:
            no_data_label = ttk.Label(self.parent, text="无符合条件的飞机",
                                     style='Info.TLabel')
            no_data_label.pack(pady=20)
            return

        # 配置父容器的grid权重
        self.parent.rowconfigure(1, weight=1)
        self.parent.columnconfigure(0, weight=1)

        # 全选控制框架
        control_frame = ttk.Frame(self.parent)
        control_frame.grid(row=0, column=0, sticky=(W, E), padx=8, pady=(5, 8))

        self.select_all_var = BooleanVar()
        self.select_all_var.set(True)  # 默认全选
        self.select_all_cb = ttk.Checkbutton(
            control_frame,
            text=f"全选 ({len(self.aircraft_list)}架)",
            variable=self.select_all_var,
            command=self.toggle_all_selection
        )
        self.select_all_cb.pack(side=LEFT)

        # 创建容器框架来容纳Canvas和滚动条
        container_frame = ttk.Frame(self.parent)
        container_frame.grid(row=1, column=0, sticky=(W, E, N, S), padx=5, pady=5)

        # 滚动框架 - 设置合适高度以激活滚动条，保持足够宽度显示飞机号
        canvas = Canvas(container_frame, height=120, width=430, bg='white', highlightthickness=1, relief='sunken')

        # 创建适中宽度的滚动条
        style = ttk.Style()
        style.configure("Thick.Vertical.TScrollbar",
                       arrowsize=20,  # 箭头大小
                       width=20)      # 滚动条宽度

        scrollbar = ttk.Scrollbar(container_frame, orient="vertical", command=canvas.yview,
                                 style="Thick.Vertical.TScrollbar")
        scrollable_frame = ttk.Frame(canvas)

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 创建飞机选择框 - 使用4列布局
        cols = 4
        created_count = 0
        for i, aircraft in enumerate(sorted(self.aircraft_list)):
            var = BooleanVar()
            var.set(True)  # 默认选中
            self.aircraft_vars[aircraft] = var

            cb = ttk.Checkbutton(
                scrollable_frame,
                text=aircraft,
                variable=var,
                command=self.update_select_all_state
            )
            cb.grid(row=i//cols, column=i%cols, sticky=W, padx=5, pady=2)
            created_count += 1



        # 配置网格 - 增加列宽减少右侧空白
        for col in range(cols):
            scrollable_frame.columnconfigure(col, weight=0, minsize=105)

        # 强制更新滚动区域
        scrollable_frame.update_idletasks()
        canvas.configure(scrollregion=canvas.bbox("all"))

        # 绑定配置更新事件
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        # 配置container_frame的grid权重
        container_frame.rowconfigure(0, weight=1)
        container_frame.columnconfigure(0, weight=1)

        # 使用grid布局Canvas和滚动条
        canvas.grid(row=0, column=0, sticky=(W, E, N, S))
        scrollbar.grid(row=0, column=1, sticky=(N, S))

        # 添加鼠标滚轮支持
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind("<MouseWheel>", _on_mousewheel)

    def toggle_all_selection(self):
        """切换全选状态"""
        select_all = self.select_all_var.get()
        for var in self.aircraft_vars.values():
            var.set(select_all)

        if select_all:
            self.selected_aircraft = set(self.aircraft_list)
        else:
            self.selected_aircraft.clear()

    def update_select_all_state(self):
        """更新全选状态"""
        selected_count = sum(1 for var in self.aircraft_vars.values() if var.get())
        total_count = len(self.aircraft_vars)

        if selected_count == total_count:
            self.select_all_var.set(True)
            self.select_all_cb.config(text=f"全选 ({total_count}架)")
        elif selected_count == 0:
            self.select_all_var.set(False)
            self.select_all_cb.config(text=f"全选 ({total_count}架)")
        else:
            self.select_all_var.set(False)
            self.select_all_cb.config(text=f"已选 {selected_count}/{total_count}架")

        # 更新选中的飞机列表
        self.selected_aircraft = {
            aircraft for aircraft, var in self.aircraft_vars.items()
            if var.get()
        }

    def get_selected_aircraft(self):
        """获取选中的飞机列表"""
        return list(self.selected_aircraft)


class PCCardCopyApp:
    def __init__(self, root):
        self.root = root
        self.root.title("FIM_PC")
        self.root.geometry("500x620")
        self.root.resizable(True, True)

        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')

        # 简单的字体设置
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 10, 'bold'))
        style.configure('Info.TLabel', font=('Arial', 9))
        style.configure('TCheckbutton', font=('Arial', 9))

        self.running = False
        self.last_update_time = 0
        self.update_interval = 0.1  # 更新间隔(秒)

        # 进度跟踪变量
        self.total_folders = 0
        self.copied_folders = 0
        self.start_time = 0
        self.current_task = ""
        self.aircraft_selector = None

        # 默认日期设置 - 5天前到今天
        today = date.today()
        default_start_date = today - timedelta(days=5)
        default_end_date = today

        self.create_widgets(default_start_date, default_end_date)

        # 启动定时更新
        self.update_time_display()

    def update_time_display(self):
        if self.running and hasattr(self, 'start_time'):
            elapsed = time.time() - self.start_time

            # 格式化已用时间
            if elapsed < 3600:  # 小于1小时
                elapsed_str = f"{int(elapsed//60):02d}:{int(elapsed%60):02d}"
            else:  # 大于等于1小时
                hours = int(elapsed // 3600)
                minutes = int((elapsed % 3600) // 60)
                seconds = int(elapsed % 60)
                elapsed_str = f"{hours}:{minutes:02d}:{seconds:02d}"

            # 计算剩余时间
            if self.copied_folders > 0 and self.total_folders > self.copied_folders:
                avg_time_per_folder = elapsed / self.copied_folders
                remaining_folders = self.total_folders - self.copied_folders
                remaining = avg_time_per_folder * remaining_folders

                if remaining < 3600:  # 小于1小时
                    remaining_str = f"{int(remaining//60):02d}:{int(remaining%60):02d}"
                else:  # 大于等于1小时
                    hours = int(remaining // 3600)
                    minutes = int((remaining % 3600) // 60)
                    seconds = int(remaining % 60)
                    remaining_str = f"{hours}:{minutes:02d}:{seconds:02d}"
            else:
                remaining_str = "--:--"

            # 添加处理速度信息
            if self.copied_folders > 0 and elapsed > 0:
                speed = self.copied_folders / elapsed * 60  # 每分钟处理的文件夹数
                speed_text = f" | 速度: {speed:.1f}个/分钟"
            else:
                speed_text = ""

            self.time_info_label.config(text=f"已用时间: {elapsed_str}  |  剩余时间: {remaining_str}")

        # 每隔100毫秒更新一次
        self.root.after(100, self.update_time_display)

    def create_widgets(self, default_start_date, default_end_date):
        # 配置根窗口
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # 主框架
        mainframe = ttk.Frame(self.root, padding="12")
        mainframe.grid(row=0, column=0, sticky=(W, E, N, S))
        mainframe.columnconfigure(0, weight=1)

        # 标题
        title_label = ttk.Label(mainframe, text="FIM_PC 飞机数据复制工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, pady=(0, 12))

        # 输入参数框架
        input_frame = ttk.LabelFrame(mainframe, text="参数设置", padding="10")
        input_frame.grid(row=1, column=0, sticky=(W, E), pady=(0, 8))
        input_frame.columnconfigure(1, weight=1)

        # 制造商选择
        ttk.Label(input_frame, text="制造商:", style='Heading.TLabel').grid(row=0, column=0, sticky=W, pady=5)
        self.manufacturer_var = StringVar()
        self.manufacturer_cb = ttk.Combobox(input_frame, textvariable=self.manufacturer_var,
                                            width=18, state="readonly")
        self.manufacturer_cb.grid(row=0, column=1, sticky=W, pady=5, padx=(10, 0))
        self.manufacturer_cb.bind('<<ComboboxSelected>>', self.on_manufacturer_changed)

        # 开始日期
        ttk.Label(input_frame, text="开始日期:", style='Heading.TLabel').grid(row=1, column=0, sticky=W, pady=5)
        self.start_date = tkcal.DateEntry(input_frame, width=18, date_pattern='y-mm-dd')
        self.start_date.grid(row=1, column=1, sticky=W, pady=5, padx=(10, 0))
        self.start_date.set_date(default_start_date)

        # 结束日期
        ttk.Label(input_frame, text="结束日期:", style='Heading.TLabel').grid(row=2, column=0, sticky=W, pady=5)
        self.end_date = tkcal.DateEntry(input_frame, width=18, date_pattern='y-mm-dd')
        self.end_date.grid(row=2, column=1, sticky=W, pady=5, padx=(10, 0))
        self.end_date.set_date(default_end_date)

        # 飞机号选择框架
        aircraft_frame = ttk.LabelFrame(mainframe, text="飞机号", padding="8")
        aircraft_frame.grid(row=2, column=0, sticky=(W, E), pady=(0, 8))
        aircraft_frame.columnconfigure(0, weight=1)

        # 飞机号选择器容器
        self.aircraft_selector_frame = ttk.Frame(aircraft_frame)
        self.aircraft_selector_frame.grid(row=0, column=0, sticky=(W, E))
        self.aircraft_selector_frame.columnconfigure(0, weight=1)

        # 按钮框架
        button_frame = ttk.Frame(mainframe)
        button_frame.grid(row=3, column=0, pady=8)

        # 执行按钮
        self.copy_btn = ttk.Button(button_frame, text="PC卡复制", command=self.start_copy_process)
        self.copy_btn.grid(row=0, column=0, padx=10, ipadx=20, ipady=5)

        # 取消按钮
        self.cancel_btn = ttk.Button(button_frame, text="取消", command=self.cancel_process,
                                    state=DISABLED)
        self.cancel_btn.grid(row=0, column=1, padx=10, ipadx=20, ipady=5)

        # 进度信息框架
        progress_frame = ttk.LabelFrame(mainframe, text="进度信息", padding="10")
        progress_frame.grid(row=4, column=0, sticky=(W, E), pady=(0, 8))
        progress_frame.columnconfigure(0, weight=1)

        # 任务统计
        self.task_stats = ttk.Label(progress_frame, text="任务: 0/0 (0%)",
                                   style='Heading.TLabel')
        self.task_stats.grid(row=0, column=0, sticky=W, pady=(0, 4))

        # 进度条
        self.progress = ttk.Progressbar(progress_frame, orient=HORIZONTAL,
                                       length=450, mode='determinate')
        self.progress.grid(row=1, column=0, pady=(0, 4), sticky=(W, E))

        # 当前任务
        self.current_task_label = ttk.Label(progress_frame, text="当前任务: 无",
                                           style='Info.TLabel')
        self.current_task_label.grid(row=2, column=0, sticky=W, pady=(0, 4))

        # 时间信息（合并显示）
        self.time_info_label = ttk.Label(progress_frame, text="已用时间: 00:00  |  剩余时间: --:--",
                                        style='Info.TLabel')
        self.time_info_label.grid(row=3, column=0, sticky=W, pady=(0, 2))

        # 初始化制造商列表
        self.init_manufacturers()

    def init_manufacturers(self):
        """初始化制造商列表"""
        # 获取所有有效的制造商
        manufacturers = set()
        for aircraft_type in AIRCRAFT_TYPE_MAP.values():
            if aircraft_type in MANUFACTURER_MAP:
                manufacturers.add(MANUFACTURER_MAP[aircraft_type])

        # ALL排在最前面，其他按字母排序
        manufacturer_list = ["ALL"] + sorted(manufacturers)
        self.manufacturer_cb['values'] = manufacturer_list
        self.manufacturer_cb.current(0)  # 默认选择ALL

        # 初始化飞机号选择器
        self.update_aircraft_selector()

    def on_manufacturer_changed(self, event=None):
        """制造商选择改变时的回调"""
        self.update_aircraft_selector()

    def update_aircraft_selector(self):
        """更新飞机号选择器"""
        manufacturer = self.manufacturer_var.get()

        # 获取符合条件的飞机列表
        if manufacturer == "ALL":
            aircraft_list = list(AIRCRAFT_TYPE_MAP.keys())
        else:
            aircraft_list = []
            for aircraft, aircraft_type in AIRCRAFT_TYPE_MAP.items():
                if MANUFACTURER_MAP.get(aircraft_type) == manufacturer:
                    aircraft_list.append(aircraft)

        # 创建新的飞机选择器
        self.aircraft_selector = AircraftSelector(self.aircraft_selector_frame, aircraft_list)

    def get_selected_aircraft(self):
        """获取选中的飞机列表"""
        if self.aircraft_selector:
            return self.aircraft_selector.get_selected_aircraft()
        return []

    def start_copy_process(self):
        # 获取用户输入
        start_date = self.start_date.get_date()
        end_date = self.end_date.get_date()
        selected_aircraft = self.get_selected_aircraft()

        # 验证输入
        if start_date > end_date:
            messagebox.showerror("错误", "结束日期不能早于开始日期!")
            return

        if not selected_aircraft:
            messagebox.showerror("错误", "请至少选择一架飞机!")
            return

        # 设置运行状态
        self.running = True
        self.copy_btn.config(state=DISABLED)
        self.cancel_btn.config(state=NORMAL)

        # 重置进度
        self.total_folders = 0
        self.copied_folders = 0
        self.start_time = time.time()
        self.progress['value'] = 0
        self.update_progress_info("准备开始处理...")

        # 在新线程中执行拷贝操作
        threading.Thread(target=self.copy_pccard_data,
                         args=(selected_aircraft, start_date, end_date),
                         daemon=True).start()

    def cancel_process(self):
        self.running = False
        self.update_progress_info("正在取消任务...")
        self.copy_btn.config(state=NORMAL)
        self.cancel_btn.config(state=DISABLED)

    def clean_target_directories(self, selected_aircraft):
        """清理LOCAL_PATH目录下所有飞机号目录内的所有文件和文件夹

        LOCAL_PATH目录结构：
        - 第0级: D:\AirFASE\FIMRoot (LOCAL_PATH)
        - 第1级: 机型目录 (如A320, ARJ21) - 遍历所有机型目录
        - 第2级: 飞机号目录 (如B-650S, B-1011) - 遍历所有飞机号目录
        - 第3级: 所有文件和文件夹 (如B-650S_202507130000.pc等) - 这些都需要删除

        Args:
            selected_aircraft: 选中的飞机号列表（此参数在此函数中不使用，清理所有飞机）
        """
        try:
            self.update_progress_info("开始清理LOCAL_PATH下所有飞机目录...")

            # 检查LOCAL_PATH是否存在
            if not os.path.exists(LOCAL_PATH):
                self.update_progress_info(f"LOCAL_PATH {LOCAL_PATH} 不存在，跳过清理")
                return

            # 遍历所有机型目录（第1级）
            total_deleted = 0
            for aircraft_type_dir in os.listdir(LOCAL_PATH):
                if not self.running:
                    break

                aircraft_type_path = os.path.join(LOCAL_PATH, aircraft_type_dir)

                # 确保是目录
                if not os.path.isdir(aircraft_type_path):
                    continue

                self.update_progress_info(f"正在清理机型目录: {aircraft_type_dir}")

                # 遍历该机型下的所有飞机号目录（第2级）
                try:
                    for aircraft_dir in os.listdir(aircraft_type_path):
                        if not self.running:
                            break

                        aircraft_path = os.path.join(aircraft_type_path, aircraft_dir)

                        # 确保是目录
                        if not os.path.isdir(aircraft_path):
                            continue

                        self.update_progress_info(f"正在清理飞机目录: {aircraft_type_dir}/{aircraft_dir}")

                        # 清理该飞机目录下的所有文件和文件夹（第3级）
                        try:
                            items = os.listdir(aircraft_path)
                            if items:
                                self.update_progress_info(f"飞机 {aircraft_dir} 目录包含 {len(items)} 个项目，开始删除...")

                                for item in items:
                                    if not self.running:
                                        break

                                    item_path = os.path.join(aircraft_path, item)

                                    try:
                                        if os.path.isfile(item_path):
                                            # 删除文件
                                            os.remove(item_path)
                                            self.update_progress_info(f"已删除文件: {aircraft_type_dir}/{aircraft_dir}/{item}")
                                            total_deleted += 1
                                        elif os.path.isdir(item_path):
                                            # 删除文件夹及其所有内容
                                            shutil.rmtree(item_path)
                                            self.update_progress_info(f"已删除文件夹: {aircraft_type_dir}/{aircraft_dir}/{item}")
                                            total_deleted += 1

                                    except Exception as e:
                                        self.update_progress_info(f"删除 {aircraft_type_dir}/{aircraft_dir}/{item} 时出错: {str(e)}")

                            else:
                                self.update_progress_info(f"飞机 {aircraft_dir} 目录为空，无需清理")

                        except Exception as e:
                            self.update_progress_info(f"清理飞机目录 {aircraft_type_dir}/{aircraft_dir} 时出错: {str(e)}")

                except Exception as e:
                    self.update_progress_info(f"遍历机型目录 {aircraft_type_dir} 时出错: {str(e)}")

            self.update_progress_info(f"LOCAL_PATH清理完成，共删除 {total_deleted} 个项目")

        except Exception as e:
            self.update_progress_info(f"清理LOCAL_PATH时出错: {str(e)}")

    def aircraft_type(self, aircraft):
        return AIRCRAFT_TYPE_MAP.get(aircraft, "")

    def air_manuf(self, air_type):
        return MANUFACTURER_MAP.get(air_type, "UNKNOWN")

    def copy_pccard_data(self, selected_aircraft, start_date, end_date):
        try:
            # 创建临时文件夹
            if os.path.exists(TEMP_PATH):
                shutil.rmtree(TEMP_PATH)
            os.makedirs(TEMP_PATH, exist_ok=True)

            # 获取年份
            year = start_date.year

            # 构建源路径
            source_path = os.path.join(PATH1, str(year))

            # 检查源路径是否存在
            if not os.path.exists(source_path):
                self.update_progress_info(f"错误: 路径 {source_path} 不存在")
                return

            # 在复制前先清理LOCAL_PATH目标路径的.pc文件夹
            self.update_progress_info("正在清理目标路径...")
            self.clean_target_directories(selected_aircraft)
            self.update_progress_info("目标路径清理完成，开始文件复制...")

            # 筛选存在的飞机目录
            existing_aircraft = []
            for aircraft in selected_aircraft:
                aircraft_path = os.path.join(source_path, aircraft)
                if os.path.exists(aircraft_path) and os.path.isdir(aircraft_path):
                    existing_aircraft.append(aircraft)

            if not existing_aircraft:
                self.update_progress_info("所选飞机在指定年份中没有数据")
                return

            # 统计总文件夹数
            self.total_folders = 0
            for aircraft in existing_aircraft:
                if not self.running:
                    break

                aircraft_path = os.path.join(source_path, aircraft)
                if os.path.exists(aircraft_path):
                    for date_dir in os.listdir(aircraft_path):
                        if self.is_valid_date_dir(date_dir, start_date, end_date):
                            self.total_folders += 1

            self.update_progress_info(f"共找到 {self.total_folders} 个符合条件的文件夹")

            if self.total_folders == 0:
                self.update_progress_info("没有找到符合条件的文件夹")
                return

            # 开始处理每个飞机目录
            for aircraft in existing_aircraft:
                if not self.running:
                    break

                self.process_aircraft_dir(source_path, aircraft, start_date, end_date)

            if self.running:
                elapsed = time.time() - self.start_time
                elapsed_str = str(timedelta(seconds=int(elapsed)))
                self.update_progress_info(f"任务完成! 共处理 {self.copied_folders} 个文件夹，总耗时: {elapsed_str}")

        except Exception as e:
            self.update_progress_info(f"错误: {str(e)}")
        finally:
            # 清理临时文件夹
            if os.path.exists(TEMP_PATH):
                try:
                    shutil.rmtree(TEMP_PATH)
                    self.update_progress_info("临时文件夹已清理")
                except Exception as e:
                    self.update_progress_info(f"清理临时文件夹失败: {str(e)}")

            self.running = False
            self.copy_btn.config(state=NORMAL)
            self.cancel_btn.config(state=DISABLED)

    def process_aircraft_dir(self, source_path, aircraft, start_date, end_date):
        aircraft_path = os.path.join(source_path, aircraft)

        # 获取飞机类型
        ac_type = self.aircraft_type(aircraft)
        if not ac_type:
            self.update_progress_info(f"警告: 飞机 {aircraft} 没有对应的机型映射")
            return

        # 处理每个日期目录
        for date_dir in os.listdir(aircraft_path):
            if not self.running:
                return

            full_path = os.path.join(aircraft_path, date_dir)

            if not os.path.isdir(full_path):
                continue

            if not self.is_valid_date_dir(date_dir, start_date, end_date):
                continue

            # 更新当前任务
            self.current_task = f"{aircraft}/{date_dir}"
            self.update_progress_info(f"正在处理: {self.current_task}")

            # 创建临时文件夹 - 在D:\FIM_temp下创建
            temp_folder_name = f"{aircraft}_{date_dir.replace('-', '')}0000.pc"
            temp_folder_path = os.path.join(TEMP_PATH, temp_folder_name)
            os.makedirs(temp_folder_path, exist_ok=True)

            try:
                # 复制指定后缀的文件 (DAT, QAR, QA2)
                valid_extensions = {'.DAT', '.QAR', '.QA2'}
                copied_files = 0

                for file in os.listdir(full_path):
                    if not self.running:
                        break

                    file_ext = os.path.splitext(file)[1].upper()
                    if file_ext in valid_extensions:
                        src_file = os.path.join(full_path, file)
                        if os.path.isfile(src_file):  # 确保是文件而不是文件夹
                            dst_file = os.path.join(temp_folder_path, file)
                            shutil.copy2(src_file, dst_file)
                            copied_files += 1

                # 如果没有复制任何文件，删除临时文件夹并跳过
                if copied_files == 0:
                    if os.path.exists(temp_folder_path):
                        shutil.rmtree(temp_folder_path)
                    continue

                if not self.running:
                    return

                # 构建目标路径
                dest_path = os.path.join(LOCAL_PATH, ac_type, aircraft)
                os.makedirs(dest_path, exist_ok=True)

                # 复制整个临时文件夹到目标位置
                final_dest = os.path.join(dest_path, temp_folder_name)
                if os.path.exists(final_dest):
                    shutil.rmtree(final_dest)
                shutil.copytree(temp_folder_path, final_dest)

                # 更新进度
                self.copied_folders += 1
                self.update_progress()

            finally:
                # 删除临时文件夹
                if os.path.exists(temp_folder_path):
                    shutil.rmtree(temp_folder_path)

    def is_valid_date_dir(self, dir_name, start_date, end_date):
        try:
            # 检查目录名是否符合yyyy-mmdd格式
            if len(dir_name) != 9 or dir_name[4] != '-':
                return False

            year = int(dir_name[:4])
            month = int(dir_name[5:7])
            day = int(dir_name[7:9])

            # 验证日期有效性
            if not (1 <= month <= 12) or not (1 <= day <= 31):
                return False

            # 将目录日期转换为date对象进行比较
            dir_date = date(year, month, day)
            return start_date <= dir_date <= end_date

        except:
            return False

    def update_progress(self):
        # 计算进度
        progress = (self.copied_folders / self.total_folders) * 100 if self.total_folders > 0 else 0

        # 更新进度条
        self.progress['value'] = progress

        # 更新任务统计 - 添加更详细的信息
        self.task_stats.config(text=f"任务进度: {self.copied_folders}/{self.total_folders} ({progress:.1f}%)")

        # 更新当前任务 - 添加颜色和样式
        if self.current_task:
            self.current_task_label.config(text=f"正在处理: {self.current_task}")
        else:
            self.current_task_label.config(text="当前任务: 无")

        # 强制更新界面
        self.root.update_idletasks()

    def update_progress_info(self, message):
        """更新进度信息"""
        self.current_task_label.config(text=message)
        self.root.update()

if __name__ == "__main__":
    root = Tk()
    app = PCCardCopyApp(root)
    root.mainloop()
