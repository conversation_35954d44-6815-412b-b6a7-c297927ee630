===============================================================================
                          安全文件同步工具 v1.0.0
                      使用私有协议的隐蔽文件同步软件
===============================================================================

📁 文件说明
-------------------------------------------------------------------------------
SecureSync_Fixed.exe    - 主程序文件 (14.4MB) - 修复版本
启动安全同步工具.bat     - 快速启动脚本
使用说明.txt            - 本说明文档

注意: 请使用 SecureSync_Fixed.exe，这是修复了服务端启动问题的版本

🚀 快速开始
-------------------------------------------------------------------------------

方法一: 使用启动脚本 (推荐)
1. 双击运行 "启动安全同步工具.bat"
2. 选择运行模式 (图形界面/服务端/客户端)
3. 按提示配置参数

方法二: 直接运行程序
1. 双击 SecureSync_Fixed.exe 启动图形界面
2. 或者通过命令行运行不同模式

🖥️ 图形界面使用 (推荐新手)
-------------------------------------------------------------------------------

1. 启动程序后会看到多个标签页:
   - 服务端: 配置和启动文件同步服务
   - 客户端: 连接服务端进行同步
   - 设置: 安全和高级配置
   - 日志: 查看运行日志

2. 服务端配置:
   - 同步目录: 选择要同步的文件夹
   - 监听地址: 通常保持默认 0.0.0.0
   - 监听端口: 默认 23847 (可修改)
   - 点击"启动服务端"

3. 客户端配置:
   - 同步目录: 选择本地同步文件夹
   - 服务器地址: 输入服务端电脑的IP地址
   - 服务器端口: 与服务端设置一致
   - 客户端ID: 唯一标识，如 client_001
   - 点击"连接服务器"

💻 命令行使用 (高级用户)
-------------------------------------------------------------------------------

启动图形界面:
SecureSync_Fixed.exe gui

启动服务端:
SecureSync_Fixed.exe server -d "C:\同步文件夹" --host 0.0.0.0 --port 23847

启动客户端:
SecureSync_Fixed.exe client -d "C:\本地文件夹" -s ************* --port 23847 --client-id client_001

🔒 安全特性
-------------------------------------------------------------------------------

隐蔽性设计:
✓ 使用非常用端口 (23847)，避免被扫描软件检测
✓ 自定义二进制协议，难以被网络分析工具识别
✓ 数据包伪装成HTTP流量，增强隐蔽性
✓ 随机填充和延迟，避免流量特征分析

加密安全:
✓ 多层数据加密保护
✓ 会话密钥机制
✓ 数据完整性验证
✓ 用户身份认证

🌐 网络配置
-------------------------------------------------------------------------------

防火墙设置:
1. Windows防火墙可能会弹出提示，请选择"允许访问"
2. 确保端口 23847 (或自定义端口) 允许通信
3. 如果仍无法连接，手动添加防火墙规则

网络要求:
- 服务端和客户端需要在同一局域网
- 或通过VPN建立网络连接
- 确保网络连通性 (可用ping命令测试)

IP地址查看:
- Windows: 打开命令提示符，输入 ipconfig
- 查找 "IPv4 地址" 即为本机IP

🔧 常见问题
-------------------------------------------------------------------------------

Q: 客户端无法连接服务端？
A: 1. 检查服务端是否正常启动
   2. 确认IP地址和端口设置正确
   3. 检查防火墙设置
   4. 使用ping命令测试网络连通性

Q: 文件没有同步？
A: 1. 检查目录权限
   2. 确认磁盘空间充足
   3. 查看程序日志了解详情
   4. 重启服务端和客户端

Q: 程序运行缓慢？
A: 1. 避免同时传输过多大文件
   2. 检查网络带宽使用情况
   3. 确保磁盘性能良好

Q: 如何更改端口？
A: 1. 图形界面: 在设置中修改端口号
   2. 命令行: 使用 --port 参数指定
   3. 服务端和客户端必须使用相同端口

Q: Windows Server 上启动服务端卡住？
A: 1. 以管理员身份运行程序
   2. 运行 "Windows Server诊断工具.bat" 进行诊断
   3. 检查防火墙和安全策略设置
   4. 尝试使用不同端口 (23848, 23849)
   5. 运行 "简化服务端测试.py" 进行基础测试
   6. 查看详细日志文件 secure_sync.log

⚠️ 注意事项
-------------------------------------------------------------------------------

安全建议:
- 仅在可信的局域网环境中使用
- 定期更换密码和密钥
- 避免在公网直接暴露服务
- 重要数据请提前备份

性能建议:
- 大文件传输建议使用有线网络
- 避免在系统盘根目录设置同步文件夹
- 定期清理日志文件

使用限制:
- 单个文件最大支持 1GB
- 建议同时连接客户端不超过 10 个
- 文件名请避免特殊字符

📞 技术支持
-------------------------------------------------------------------------------

如遇到问题:
1. 查看程序日志 (日志标签页)
2. 检查 secure_sync.log 日志文件
3. 参考本说明文档的常见问题部分

程序特点:
- 绿色软件，无需安装
- 单文件运行，便于部署
- 支持 Windows 7/8/10/11
- 兼容 32位 和 64位 系统

===============================================================================
版本: v1.0.0
更新日期: 2025年8月
===============================================================================
