航班数据分析器 v2.0 - 使用说明
========================================

📋 软件简介
--------
航班数据分析器是一个专业的PC卡数据分析与文件复制工具，主要用于：
- 分析MSG.DAT文件中的航班数据
- 提取飞机号和日期信息
- 自动复制相关数据文件到指定目录
- 提供直观的GUI界面和实时进度显示

🚀 快速开始
--------
1. 双击"航班数据分析器.exe"启动程序
2. 在"输入文件夹"中选择包含PC卡数据的根目录（默认：Z:\PC卡）
3. 在"目标文件夹"中选择数据备份的目标目录（默认：Z:\DATA_BAK\FDIMU_PC）
4. 点击"MSG读取"按钮开始分析数据
5. 分析完成后，点击"PC卡复制"按钮复制文件

📁 文件要求
--------
输入文件夹应包含以下结构：
- 各级子文件夹中包含MSG.DAT文件
- 可选的数据文件：*.DAT, *.QAR, *.QA2
- 可选的数据文件夹：*.REP, *.REC, *.QAR

🔧 功能特点
--------
✅ 自动扫描MSG.DAT文件
✅ 智能提取飞机号（B-XXXX格式）
✅ 自动识别日期信息
✅ UTC时间自动转换为北京时间
✅ 数据一致性检查
✅ 实时进度显示
✅ 多线程处理，提高效率
✅ 自动跳过已存在的文件
✅ 详细的处理日志

📊 结果说明
--------
分析结果表格包含以下列：
- PC卡数据：文件夹名称
- 飞机号_MCC：从文件夹路径提取的飞机号
- 日期_MCC：从文件夹路径提取的日期
- 飞机号_MSG：从MSG.DAT文件提取的飞机号
- 日期_MSG：从MSG.DAT文件提取的日期
- 记录数：MSG.DAT文件中的记录总数
- 日期差：文件夹日期与MSG日期的差值（天）
- 飞机号_BAK：用于备份的最终飞机号
- 日期_BAK：用于备份的最终日期
- 数据复制：文件复制进度

🎨 界面说明
--------
- 绿色行：数据一致
- 红色行：数据不一致
- 蓝色行：正在处理中

⚙️ 系统要求
--------
- Windows 7/8/10/11 (64位)
- 无需安装Python环境
- 建议内存：4GB以上
- 建议硬盘空间：100MB以上

🔍 故障排除
--------
1. 如果程序无法启动：
   - 检查是否有杀毒软件阻止
   - 尝试以管理员身份运行

2. 如果分析失败：
   - 检查输入路径是否正确
   - 确保有足够的磁盘空间
   - 检查文件访问权限

3. 如果复制失败：
   - 检查目标路径是否可写
   - 确保有足够的磁盘空间
   - 检查文件是否被其他程序占用

📞 技术支持
--------
如遇到问题，请联系技术支持并提供：
- 错误截图
- 操作步骤
- 数据文件示例

版本：v2.0
更新日期：2025-07-11
开发者：Augment Agent

🔄 更新日志
--------
v2.0 (2025-07-11):
- 修复了numpy导入错误
- 优化了依赖库打包
- 提升了程序稳定性
- 增强了错误处理机制
