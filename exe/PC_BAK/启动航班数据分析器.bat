@echo off
chcp 65001 >nul
title 航班数据分析器 v2.0

echo.
echo ========================================
echo    航班数据分析器 v2.0
echo ========================================
echo.
echo 正在启动程序，请稍候...
echo.

REM 检查exe文件是否存在
if not exist "航班数据分析器.exe" (
    echo 错误：找不到"航班数据分析器.exe"文件
    echo 请确保此批处理文件与exe文件在同一目录中
    echo.
    pause
    exit /b 1
)

REM 启动程序
start "" "航班数据分析器.exe"

REM 等待程序启动
timeout /t 2 /nobreak >nul

echo 程序已启动！
echo.
echo 如果程序没有正常启动，请：
echo 1. 检查是否有杀毒软件阻止
echo 2. 尝试直接双击"航班数据分析器.exe"
echo 3. 以管理员身份运行此批处理文件
echo.

timeout /t 5 /nobreak >nul
exit /b 0
